# Trigger Service

A microservice for unified automated execution of tasks triggered by scheduled events, system events, and webhooks across the CRM and Perkd ecosystem.

## Table of Contents
- [Overview](#overview)
- [System Architecture](#system-architecture)
- [Trigger Lifecycle](#trigger-lifecycle)
- [Trigger Types](#trigger-types)
- [Trigger Configuration](#trigger-configuration)
- [State Management](#state-management)
- [Multi-tenancy Architecture](#multi-tenancy-architecture)
- [Service Integration](#service-integration)
- [Data Models](#data-models)
- [API Endpoints](#api-endpoints)
- [Events](#events)


## Overview

This service enables automated task execution through three trigger types: scheduled timers, system events, and webhooks. It serves as the central automation hub for the CRM and Perkd ecosystem, managing execution state, handling retries, and ensuring proper multi-tenant isolation.

### System Architecture

```mermaid
graph TD
    subgraph External[External Systems]
        CRM[CRM Services]
        Perkd[Perkd Services]
        API[API Clients]
    end

    subgraph TriggerService[Trigger Service]
        Input[Input Layer]
        Core[Core Processing]
        State[State Manager]
        Output[Output Layer]
    end

    CRM & Perkd --> |Events| Input
    API --> |Requests| Input
    Input --> Core
    Core --> State
    State --> Output
    Output --> |Callbacks| External
```

- **Event Processing**
  - Real-time and scheduled event processing
  - Condition-based execution filtering
  - Automatic state management and transitions
  - Built-in metrics and monitoring
  - Event batching and throttling support


### Core Features

- **Unified Trigger System**: Common infrastructure for Timer, Event, and Request triggers
- **State Management**: Sophisticated state transitions with validation
- **Conditional Execution**: Advanced condition checking with custom operations
- **Multi-tenancy**: Built-in support for multiple tenant environments
- **Automatic Cleanup**: Housekeeping functionality for expired triggers
- **Metrics Tracking**: Built-in performance and execution metrics

## Trigger Lifecycle
The following diagram illustrates the complete lifecycle of a trigger from creation to cleanup:

```mermaid
graph LR
    Creation[Trigger Creation] --> Validation[Validation]
    Validation --> Scheduling[Scheduling]
    Scheduling --> Execution[Execution]
    Execution --> |Success| Completion[Completion]
    Execution --> |Failure| Retry[Retry Logic]
    Retry --> Execution
    Execution --> |End Condition| Cleanup[Cleanup]
```

## Trigger Types
The service supports three types of triggers that share a common infrastructure but serve different automation needs:

### Timer Triggers
- Executes actions at specified times or intervals
- Supports various time formats:
  - Date objects
  - Date strings
  - Human-readable intervals (e.g., "in 15 seconds", "tomorrow at 2pm")
  - Cron expressions
- Features:
  - Configurable repeat intervals
  - Optional repeat count limits
  - Timezone support
  - Automatic state management

#### Timer Processing Flow
The following diagram shows how timer-based triggers are processed:

```mermaid
graph LR
    Time[Time Check] --> Parse[Parse Schedule]
    Parse --> Validate[Validate Time]
    Validate --> Queue[Schedule Queue]
    Queue --> Execute[Execute Callback]
    Execute --> |Success| Update[Update Next Run]
    Execute --> |Failure| Retry[Retry Queue]
    Update --> |Has Repeats| Time
    Update --> |No Repeats| Complete[Complete]
```

### Event Triggers
- Responds to system events across the platform
- Listens for domain-specific events (e.g., card.card.issued, membership.member.created)
- Features:
  - Condition-based filtering
  - Event batching support
  - Multi-event listening
  - Automatic cleanup after execution

#### Event Processing Flow
The following diagram shows how events are processed through the system:

```mermaid
graph LR
    Event[Event Received] --> Filter[Event Filtering]
    Filter --> Condition[Condition Check]
    Condition --> Queue[Processing Queue]
    Queue --> Execute[Execute Callback]
    Execute --> |Success| Notify[Notify Success]
    Execute --> |Failure| Retry[Retry Queue]
```

### Request Triggers
- Handles direct HTTP requests for on-demand automation
- Useful for webhook integrations and external system callbacks
- Features:
  - Request payload validation
  - Rate limiting support
  - Automatic retry handling
  - Metrics tracking

#### Request Processing Flow
The following diagram shows how request-based triggers are processed:

```mermaid
graph LR
    Request[Request Received] --> Auth[Authentication]
    Auth --> Validate[Payload Validation]
    Validate --> RateLimit[Rate Limiting]
    RateLimit --> Queue[Processing Queue]
    Queue --> Execute[Execute Callback]
    Execute --> |Success| Response[Send Response]
    Execute --> |Failure| Retry[Retry Queue]
```

All trigger types share common capabilities:
- State management (SCHEDULED, RUNNING, PAUSED, etc.)
- Webhook-based callbacks
- Start/end time controls
- Multi-tenant support
- Persistence options


## Trigger Configuration
Triggers can be configured in the following ways:

### Basic Configuration
- **name**: Required unique identifier (max 128 characters)
- **type**: Type of trigger (`timer`, `event`, `request`)
- **description**: Optional description (max 256 characters)
- **priority**: Execution priority (0-20)

### Callback Configuration
```json
{
	"callback": {
		"method": "post|get|put|delete",
		"url": "https://api.example.com/webhook",
		"payload": {
			// Optional custom payload
		}
	}
}
```

### Timer Configuration
- **repeatInterval**: Supports multiple formats:
  - Milliseconds (number)
  - Cron expressions (string)
  - Human intervals (e.g., "15 seconds", "1 day")
- **repeat**: Number of times to repeat (optional)
- **timezone**: Timezone for cron expressions (e.g., "Asia/Singapore")

### Event Configuration
- **events**: Array of event names to listen for
- **condition**: Optional condition string for filtering events
- **persistAfterEnd**: Whether to retain trigger after completion

### Time Controls
- **start**: Start time for trigger activation
- **end**: End time for trigger deactivation
- **active**: Whether the trigger is currently active

### Example Configurations

#### Timer Trigger
```json
{
  "name": "Daily Report Timer",
  "type": "timer",
  "priority": 0,
  "callback": {
    "method": "post",
    "url": "https://api.example.com/reports",
    "payload": {
      "reportType": "daily"
    }
  },
  "repeatInterval": "1 day",
  "timezone": "Asia/Singapore",
  "repeat": 30
}
```

#### Event Trigger
```json
{
  "name": "Card Issued Handler",
  "type": "event",
  "callback": {
    "method": "post",
    "url": "https://api.example.com/cards/process"
  },
  "events": ["card.card.issued"],
  "condition": "data.status === 'active'",
  "persistAfterEnd": false
}
```

#### Request Trigger
```json
{
  "name": "Webhook Handler",
  "type": "request",
  "callback": {
    "method": "post",
    "url": "https://api.example.com/webhook",
    "payload": {
      "source": "trigger-service"
    }
  }
}
```


## State Management

The trigger service implements a robust state management system that tracks and controls the lifecycle of triggers. Each trigger can exist in one of several states, with well-defined transitions between them.

### Trigger States

- **scheduled**: Initial state when a trigger is created and waiting for execution
- **running**: Active state during trigger execution
- **paused**: Temporarily suspended state
- **completed**: Final state after successful execution
- **failed**: Error state when execution encounters issues
- **ended**: Terminal state when trigger reaches end conditions

### State Transitions

```mermaid
stateDiagram-v2
	SCHEDULED --> RUNNING: start execution
	RUNNING --> COMPLETED: successful completion
	RUNNING --> FAILED: execution error
	RUNNING --> PAUSED: manual pause
	PAUSED --> RUNNING: resume
	RUNNING --> ENDED: end conditions met
	FAILED --> SCHEDULED: retry
	SCHEDULED --> ENDED: manual stop
```

### State Properties

Each state maintains specific properties:

- **scheduled**
	- `nextRunAt`: Next scheduled execution time
	- `repeatRemaining`: Number of remaining executions

- **running**
	- `lastRunAt`: Current execution start time
	- `active`: Set to true

- **paused**
	- `active`: Set to false
	- Preserves all timing information

- **completed**
	- `lastFinishedAt`: Successful completion time
	- Updates `repeatRemaining`

- **failed**
	- `failedAt`: Time of failure
	- `failReason`: Error details
	- May trigger automatic retry

- **ended**
	- All timing fields finalized
	- `active`: Set to false
	- `persistAfterEnd` determines cleanup

### State Management Features

- **Automatic Transitions**: States change automatically based on execution results
- **Manual Controls**: API endpoints for manual state changes
- **Persistence**: State changes are persisted to database
- **Event Emissions**: Each state change emits corresponding events
- **Retry Logic**: Configurable retry behavior for failed states
- **Cleanup**: Automatic cleanup of ended triggers based on configuration


## Multi-tenancy Architecture
The Trigger Service is designed with multi-tenancy as a core feature, allowing multiple tenants to use the service independently while sharing the same infrastructure.

```mermaid
graph TD
    subgraph Tenants
        T1[Tenant 1]
        T2[Tenant 2]
        T3[Tenant N]
    end
    
    subgraph TriggerService[Trigger Service]
        Router[Tenant Router]
        subgraph Processing
            P1[Processor 1]
            P2[Processor 2]
            P3[Processor N]
        end
    end
    
    T1 & T2 & T3 --> Router
    Router --> P1 & P2 & P3
```

### Key Multi-tenant Features
- **Tenant Isolation**: Each tenant's data and triggers are completely isolated
- **Resource Management**: Dedicated processing queues per tenant
- **Configuration**: Tenant-specific settings and limits
- **Monitoring**: Per-tenant metrics and logging


## Service Integration

```mermaid
graph TD
    %% External Sources Layer
    subgraph Sources[External Sources]
        direction LR
        EventBus1[Event Bus]
        API[API Endpoints]
        Card[Card Service]
        Member[Member Service]
        Campaign[Campaign Service]
        Sales[Sales Service]
        Webhook[Webhooks]
        Client[Client Services]
    end

    %% Trigger Service Core
    subgraph TriggerService[Trigger Service]
        %% Input Processing Layer
        subgraph Processing[Processing Layer]
            direction LR
            Event[Event Triggers]
            Request[Request Triggers]
            Timer[Timer Triggers]
        end

        %% Core Processing
        subgraph Core[Core Layer]
            direction LR
            TriggerMgr[Trigger Manager]
            StateEngine[State Engine]
            Scheduler[Scheduler]
            Maintenance[Maintenance]
        end

        %% State Management
        subgraph States[State Management]
            direction LR
            SCHEDULED --> RUNNING
            RUNNING --> COMPLETED
            RUNNING --> FAILED
            RUNNING --> PAUSED
            PAUSED --> RUNNING
            RUNNING --> ENDED
        end

        %% Execution Layer
        subgraph Execution[Execution Layer]
            direction LR
            CallbackMgr[Callback Manager]
        end
    end

    EventBus2[Event Bus]
    TargetServices[Target Services]

    %% Connections from Sources to Processing
    Card & Member & Campaign & Sales --> EventBus1
    EventBus1 --> Event
    Webhook & Client --> API
    API --> Request
    Scheduler --> Timer

    %% Internal Service Flows
    Event & Request & Timer --> TriggerMgr
    TriggerMgr --> StateEngine
    StateEngine --> States
    States --> CallbackMgr

    %% Output Flows
    CallbackMgr -- Lifecycle Events --> EventBus2
    CallbackMgr -. HTTP Callbacks .-> TargetServices

    %% Style Definitions
    classDef service fill:#2196F3,stroke:#1565C0,color:white
    classDef component fill:#4CAF50,stroke:#2E7D32,color:white
    classDef trigger fill:#FF9800,stroke:#EF6C00,color:white
    classDef infrastructure fill:#9C27B0,stroke:#6A1B9A,color:white
    classDef api fill:#607D8B,stroke:#455A64,color:white
    classDef execution fill:#E91E63,stroke:#C2185B,color:white
    classDef client fill:#00BCD4,stroke:#0097A7,color:white
    classDef state fill:#795548,stroke:#4E342E,color:white

    %% Apply Styles
    class Card,Member,Campaign,Sales,TargetServices service
    class TriggerMgr,StateEngine,Scheduler,Maintenance component
    class Timer,Event,Request trigger
    class EventBus1,EventBus2 infrastructure
    class API,Webhook,Client api
    class CallbackMgr execution
    class SCHEDULED,RUNNING,COMPLETED,FAILED,PAUSED,ENDED state
```

The Trigger Service processes automation through three layers:

1. **Processing Layer**
   - Handles incoming triggers from different sources
   - Event Triggers: Process events from Event Bus
   - Request Triggers: Handle API requests
   - Timer Triggers: Managed by internal Scheduler

2. **Core Layer**
   - Manages trigger lifecycle and state
   - Trigger Manager: Coordinates trigger processing
   - State Engine: Handles state transitions
   - Scheduler: Manages timer-based triggers
   - Maintenance: Handles restore and housekeeping tasks

3. **State Management Layer**
   - Manages trigger states and transitions:
     - SCHEDULED → RUNNING: When trigger execution starts
     - RUNNING → COMPLETED: On successful completion
     - RUNNING → FAILED: On execution error
     - RUNNING ↔ PAUSED: For manual control
     - RUNNING → ENDED: When end conditions met

4. **Execution Layer**
   - Manages outbound communication
   - Publishes lifecycle events to Event Bus
   - Makes HTTP callbacks to target services
   - Handles rate limiting and retries

Key integration points:
- **Event Bus**: Handles both trigger events and lifecycle events
- **API Endpoints**: Handles direct trigger requests
- **Multi-tenancy**: Built into all service components and data handling
- **Execution Layer**: Manages external communication with rate limiting and retries


### Campaign Service Integration

The Campaign Service is tightly integrated with and dependent on the Trigger Service for its core automation functionality:

```mermaid
graph TD
    subgraph CampaignService[Campaign Service]
        CampaignAPI[Campaign API]
        CampaignState[State Manager]
        CampaignEvents[Event Emitter]
    end

    subgraph TriggerService[Trigger Service]
        TriggerAPI[Trigger API]
        StateEngine[State Engine]
        Scheduler[Scheduler]
    end

    EventBus[Event Bus]

    CampaignAPI --> |1.Create triggers| TriggerAPI
    TriggerAPI --> |2.Register with scheduler| Scheduler
    Scheduler --> |3.Execute at scheduled time| TriggerAPI
    TriggerAPI --> |4.Execute campaign action| CampaignAPI
    CampaignEvents --> |5.Emit domain events| EventBus
    EventBus --> |6.Deliver events| TriggerAPI
```

> **Note on State Management**: The Campaign Service relies entirely on the Trigger Service's state management system. All state transitions (start, pause, resume, stop) are handled by the Trigger Service, with the Campaign Service accessing these through the Trigger Service's API endpoints. This ensures consistent state management across both services.

#### Event Processing Flow
```mermaid
sequenceDiagram
    participant C as Campaign Service
    participant E as Event Bus
    participant T as Trigger Service
    participant CB as Callback Handler

    C->>T: Register Campaign Trigger
    C->>E: Emit Campaign Event
    E->>T: Forward Event
    T->>T: Process Event
    T->>CB: Execute Callback
    CB->>C: Update Campaign
    C->>E: Emit Status Update
```

1. **Event-Driven Communication**:
- The Campaign Service relies on the Trigger Service to handle campaign-related events like `campaign.rsvp.success`
- Campaign events are processed through the Event Bus infrastructure, with the Trigger Service acting as the central event processor

2. **Campaign State Management**:
- The Campaign Service uses Trigger Service's state management system for campaign execution states:
  - `start`
  - `pause` 
  - `resume`
  - `end`
- This is evidenced by the campaign model endpoints that mirror Trigger Service's state transitions

3. **Automated Campaign Execution**:
- Campaigns use all three types of triggers provided by the Trigger Service:
  - Timer triggers: For scheduled campaign starts/ends
  - Event triggers: For responding to user actions and system events
  - Request triggers: For handling webhook callbacks and external integrations

4. **Campaign Workflow Automation**:
- Campaign triggers are used for:
  - Form submissions (`Form.submit`)
  - Campaign starts (`/api/Campaigns/:id/start`)
  - RSVP handling
  - Automated notifications

5. **Multi-tenancy Support**:
- Campaign Service inherits multi-tenant isolation through Trigger Service's tenant management
- Each campaign's triggers are scoped to specific tenants

6. **Callback Integration**:
- Campaign Service registers callbacks with Trigger Service for automated actions
- These callbacks are executed based on trigger conditions and states

7. **Resource Lifecycle Management**:
- Campaign resources are managed through Trigger Service's lifecycle hooks:
  - Creation
  - Scheduling
  - Execution
  - Cleanup

This tight integration means the Campaign Service relies on the Trigger Service for temporal execution (scheduling), event processing, state management, multi-tenant isolation, resource lifecycle management, and automated workflow execution. The relationship is unidirectional, with the Campaign Service being dependent on the Trigger Service, but not vice versa.


## Data Models
The service uses a single core model for managing triggers:

```mermaid
erDiagram
    Trigger {
        string id PK
        string name
        string type
        number priority
        object callback
        date start
        date end
        boolean persistAfterEnd
        string[] events
        string condition
        string repeatInterval
        number repeat
        number repeatRemaining
        string state
        date nextRunAt
        date lastRunAt
        date lastFinishedAt
        date failedAt
        object failReason
        boolean active
        boolean visible
        date createdAt
        date modifiedAt
    }
```

### Key Properties

- **Core Properties**
  - `name`: Required identifier for the trigger (max 128 characters)
  - `type`: Type of trigger (timer, event, request)
  - `priority`: Execution priority (max 20)
  - `callback`: Webhook configuration for trigger execution
  - `visible`: Controls trigger visibility in listings (default: true)

- **Timing Controls**
  - `start`: Start time for trigger activation
  - `end`: End time for trigger deactivation
  - `repeatInterval`: Timer configuration (milliseconds, cron format, or human-interval)
  - `repeat`: Number of times to repeat
  - `repeatRemaining`: Remaining repetitions
  - `timezone`: Timezone for cron expressions (e.g., "Asia/Singapore")

- **State Management**
  - `state`: Current trigger state (scheduled, running, paused, completed, failed, ended)
  - `active`: Whether the trigger is currently active
  - `persistAfterEnd`: Whether to retain trigger after completion

- **Execution Details**
  - `events`: Array of event names to listen for
  - `condition`: Condition string for execution filtering
  - `nextRunAt`: Next scheduled execution time
  - `lastRunAt`: Last execution start time
  - `lastFinishedAt`: Last execution completion time
  - `failedAt`: Time of last failure
  - `failReason`: Details of failure if any

- **Metadata**
  - `createdAt`: Timestamp of trigger creation
  - `modifiedAt`: Timestamp of last modification


## API Endpoints
For detailed API documentation, see [API Documentation](docs/API.md).

### Creation and Management
- **POST /Triggers**: Create a new trigger
- **POST /Triggers/findOrCreate**: Find an existing trigger or create a new one

### Timer Operations
- **POST /Triggers/:id/schedule**: Schedule a trigger for execution

### Event Operations
- **POST /Triggers/:id/listen**: Set the trigger to listen to specified events

### Request Operations
- **POST /Triggers/:id/handle**: Handle incoming requests for the trigger
- **POST /Triggers/:id/request**: Accept a request from the Event Gateway

### State Management
- **GET /Triggers/:id/pause**: Pause the execution of a trigger
- **GET /Triggers/:id/resume**: Resume a paused trigger
- **GET /Triggers/:id/stop**: Stop the execution of a trigger
- **DELETE /Triggers/:id**: Delete a trigger

### Maintenance
- **POST /Triggers/:id/restore**: Restore event and timer handlers for a trigger
- **POST /Triggers/:id/houseKeep**: Clean up expired triggers for a specific tenant


## Events

### Published Events
Events emitted by this service for other services to consume.

#### Core Events
- `trigger.created` - Emitted when a new trigger is created
- `trigger.updated` - Emitted when a trigger is modified
- `trigger.deleted` - Emitted when a trigger is removed
- `trigger.event` - Emitted when a trigger processes an event

#### State Events
- `trigger.scheduled` - When trigger enters SCHEDULED state
- `trigger.running` - When trigger starts execution
- `trigger.paused` - When trigger is paused
- `trigger.completed` - When trigger completes successfully
- `trigger.failed` - When trigger execution fails
- `trigger.ended` - When trigger reaches end conditions


### Subscribed Events
Events consumed by this service from other services.

#### System Events
- `eventbus.started` - When EventBus connection is established
- `eventbus.stopped` - When EventBus connection is terminated

#### Domain Events
The service can subscribe to any domain events following the pattern:
`{domain}.{actor}.{action}`

Examples from the codebase:
- `card.card.issued`
- `card.card.accepted` 
- `card.card.declined`
- `card.card.registered`
- `membership.member.created`
- `membership.membership.joined`
- `campaign.rsvp.success`

### Event Structure
```json
{
  "id": "unique_event_id",
  "name": "event.name",
  "domain": "service_domain",
  "actor": "entity_type",
  "action": "operation",
  "data": {
    // Event specific payload
  },
  "tenantCode": "tenant_identifier",
  "timezone": "Asia/Singapore",
  "published": "timestamp"
}
```

### Event Processing
- Parallel processing of multiple event triggers
- Built-in rate limiting
- Automatic batching of event callbacks
- Configurable consumer and bus limits
- Event filtering and condition checking before processing

### Event Processing Configuration
- `MAXLEN`: Maximum length of event queue
- `LIMIT`: Rate limiting threshold
- `limitConsumer`: Consumer-specific rate limits
- `limitBus`: Event bus rate limits
