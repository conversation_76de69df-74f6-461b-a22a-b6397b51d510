# Logs
logs
*.log

# Environment Variables
.env

# Runtime data
pids
*.pid
*.seed

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
.nyc_output
coverage

# Compiled binary addons (http://nodejs.org/api/addons.html)
build/Release

# Dependency directory
# Commenting this out is preferred by some people, see
# https://www.npmjs.org/doc/misc/npm-faq.html#should-i-check-my-node_modules-folder-into-git-
node_modules

# Users Environment Variables
.lock-wscript

# Ignore mac DS_Store file
.DS_Store

# Ignore common module
server/lib/clients

# Ignore common module
client

# Ignore .idea
.idea

# Ignore db.json
db.json

# Yarn
.yarn/install-state.gz

# Merge Branch
copilot/.workspace
copilot/trigger/manifest.yml
server/model-config.json
server/config.json
server/datasources.local.js
server/config/eventbus.json
server/config/metrics.json
Dockerfile
.clinerules
.cursorrules
.perkdrules
.crmrules
!branch-config/*/Dockerfile
!branch-config/*/.cursorrules
!branch-config/*/.clinerules
