
var Trigger = require('trigger');


//-----  request triggers

var formHand<PERSON> = new Trigger({
	webHook: {
		method: 'post',
		url: 'api.perkd.me/campaign/'
	},
	param: {
		campaignId: '',
	},
	start: 'tomorrow at 2pm',			// default to immediately
	end: '3 days later at 4pm',			// default to infinity  (until cancelled)
});
formHandler.duration(from, to);
formHandler.save();

formHandler.handle('Form.submit');



//-----  event triggers

var eventResponder = new Trigger({
	webHook: {
		method: 'post',
		url: 'api.perkd.me/campaign/'
	},
	param: {
		campaignId: '',
	},
	start: 'tomorrow at 2pm',			// default to immediately
	end: '3 days later at 4pm',			// default to infinity  (until cancelled)
});

eventResponder.create();
eventResponder.update();


eventResponder.listen('Form.submit');



//-----  timer triggers

var timedTrigger = new Trigger({
	webHook: {
		method: 'get',
		url: 'api.perkd.me/dataExchange/upload'
	},
	param: {
		campaignId: '',
	},
	repeatInterval: '1 day',
	end: 'end of 2018'
});
timedTrigger.save();						//  trigger becomes active

timedTrigger.schedule('6 minutes later');	// until infinity or cancelled
timedTrigger.cancel();

timedTrigger.repeatEvery(interval, [times]);	// repeat every interval for [optional] number of times

