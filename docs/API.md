# Trigger Service API Documentation

## Base URL
```
http://{host}:8012/api/Triggers
```

## Authentication
All requests require a `tenant-code` header for multi-tenancy support.

## Common Response Structure
All responses follow this basic structure:
```json
{
	"id": "string",
	"name": "string",
	"type": "string",
	"priority": "number",
	"state": "string",
	"active": "boolean",
	"callback": {
		"method": "string",
		"url": "string",
		"payload": "object"
	},
	"start": "date",
	"end": "date",
	"nextRunAt": "date",
	"lastRunAt": "date",
	"lastFinishedAt": "date",
	"createdAt": "date",
	"modifiedAt": "date"
}
```

## Endpoints

### Create Trigger
Creates a new trigger.

**POST** `/Triggers`

#### Request Body
```json
{
	"name": "Timer trigger #1",
	"priority": 0,
	"callback": {
		"method": "post",
		"url": "https://api.example.com/webhook",
		"payload": {
			"name": "example"
		}
	},
	"type": "timer|event|request",
	"repeatInterval": "string",
	"repeatTimezone": "string",
	"repeat": "number",
	"persistAfterEnd": "boolean",
	"events": ["string"],
	"condition": "string",
	"start": "date",
	"end": "date"
}
```

#### Response
```json
{
	"id": "unique_trigger_id",
	"name": "Timer trigger #1",
	"priority": 0,
	"state": "SCHEDULED",
	"active": true,
	"callback": {
		"method": "post",
		"url": "https://api.example.com/webhook",
		"payload": {
			"name": "example"
		}
	},
	"createdAt": "2024-03-21T10:00:00.000Z",
	"modifiedAt": "2024-03-21T10:00:00.000Z"
}
```

### Find or Create Trigger
Find an existing trigger or create a new one if not found.

**POST** `/Triggers/findOrCreate`

#### Request Body
```json
{
	"filter": {
		"where": {
			"name": "Timer trigger #1"
		}
	},
	"data": {
		"name": "Timer trigger #1",
		"priority": 0,
		"callback": {
			"method": "post",
			"url": "https://api.example.com/webhook"
		}
	}
}
```

#### Response
```json
{
	"data": {
		"id": "trigger_id",
		"name": "Timer trigger #1",
		"state": "SCHEDULED"
	},
	"created": true
}
```

### Schedule Timer
Schedules a timer trigger for execution.

**POST** `/Triggers/{id}/schedule`

#### Request Body
```json
{
	"when": "30 seconds"
}
```

#### Response
```json
{
	"id": "trigger_id",
	"state": "SCHEDULED",
	"nextRunAt": "2024-03-21T10:00:30.000Z"
}
```

### Listen to Events
Sets up event trigger to listen for specific events.

**POST** `/Triggers/{id}/listen`

#### Request Body
```json
{
	"events": ["event.name.action"]
}
```

#### Response
```json
{
	"id": "trigger_id",
	"state": "SCHEDULED",
	"events": ["event.name.action"]
}
```

### Handle Request
Sets up request trigger to handle incoming requests.

**POST** `/Triggers/{id}/handle`

#### Response
```json
{
	"id": "trigger_id",
	"state": "SCHEDULED",
	"type": "request"
}
```

### Process Request
Processes an incoming request for a request trigger.

**POST** `/Triggers/{id}/request`

#### Request Body
```json
{
	// Request specific payload
}
```

#### Response
```json
{
	"id": "trigger_id",
	"state": "COMPLETED",
	"lastRunAt": "2024-03-21T10:00:00.000Z",
	"lastFinishedAt": "2024-03-21T10:00:01.000Z"
}
```

### Pause Trigger
Pauses a trigger's execution.

**GET** `/Triggers/{id}/pause`

#### Query Parameters
- `when` (optional): When to pause the trigger

#### Response
```json
{
	"id": "trigger_id",
	"state": "PAUSED"
}
```

### Resume Trigger
Resumes a paused trigger.

**GET** `/Triggers/{id}/resume`

#### Query Parameters
- `when` (optional): When to resume the trigger

#### Response
```json
{
	"id": "trigger_id",
	"state": "SCHEDULED"
}
```

### Stop Trigger
Stops a trigger's execution.

**GET** `/Triggers/{id}/stop`

#### Query Parameters
- `when` (optional): When to stop the trigger

#### Response
```json
{
	"id": "trigger_id",
	"state": "ENDED",
	"active": false
}
```

### Delete Trigger
Deletes a trigger.

**DELETE** `/Triggers/{id}`

#### Response
```json
{
	"count": 1
}
```

### Restore Trigger
Restores event and timer handlers for a trigger.

**POST** `/Triggers/{id}/restore`

#### Response
```json
{
	"id": "trigger_id",
	"state": "RESTORED"
}
```

### Housekeep
Clean up expired triggers for a specific tenant.

**POST** `/Triggers/{id}/houseKeep`

#### Response
```json
{
	"count": 1,
	"message": "Housekeeping completed"
}
```

## States
A trigger can be in one of these states:
- `SCHEDULED`: Initial state when trigger is created and waiting for execution
- `RUNNING`: Active state during trigger execution
- `PAUSED`: Temporarily suspended state
- `COMPLETED`: Final state after successful execution
- `FAILED`: Error state when execution encounters issues
- `ENDED`: Terminal state when trigger reaches end conditions

## Error Responses
```json
{
	"error": {
		"statusCode": 404,
		"name": "Error",
		"message": "Trigger not found",
		"code": "TRIGGER_NOT_FOUND"
	}
}
```

Common error codes:
- 400: Bad Request - Invalid input parameters
- 401: Unauthorized - Missing or invalid tenant-code
- 404: Not Found - Trigger does not exist or trigger not started/paused
- 409: Conflict - Trigger already exists
- 500: Internal Server Error

## Rate Limiting
The service includes built-in rate limiting with the following defaults:
- Limit: 100 requests
- Interval: 1000ms (1 second) 