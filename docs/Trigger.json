{"event": "location/ time / inApp", "maximumTimes": "0 ~ N (number)", "conditions": {"location": {"direction": "enter/leave", "geo": {"latitude": 45.344, "longitude": 45.333, "distance": 100}, "geo(2)": {"geojson": {"type": "Feature/ FeatureCollection", "geometry": {"type": "Polygon", "coordinates": [[[-122.65, 45.55], [-122.65, 45.5]]]}}}}, "time": [{"atTime": "2015-01-01T09:00:00+08:00"}, {"timeOfDay": "09:00", "dayOfWeek": ["Wed", "<PERSON><PERSON>"]}, {"timeOfDay": "09:00", "dayOfMonth": [1, 2]}], "inApp": {"action": "launch", "minAppVersion": "can be used for app launch & location", "maxAppVersion": "can be used for app launch & location"}}, "timeRange": [], "notice": {"localNotification": "You've received a new message", "badge": "+1", "sound": "glass"}, "actions": [{"class": "Offer", "method": "issue", "parameters": {"offerTemplateId": " "}}], "actions(2)": "callbackUrl", "interactions": [{}]}