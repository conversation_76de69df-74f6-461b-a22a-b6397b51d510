const LOCALHOST = '127.0.0.1',
	{
		NODE_ENV,
		// DB_HOST = NODE_ENV ? 'mongodb' : LOCALHOST,
		DB_HOST = '*************',
		DB_USERNAME = '',
		DB_PASSWORD = '',
		DB_AUTH = 'admin',
		DB_SET = '',
	} = process.env

module.exports = {
	default: {
		name: 'default',
		connector: 'mongodb',
		url: 'mongodb://' + (DB_USERNAME ? `${DB_USERNAME}:${DB_PASSWORD}@` : '') + `${DB_HOST}/perkd?authSource=${DB_AUTH}` + (DB_SET ? `&replicaSet=${DB_SET}&readPreference=primaryPreferred&slaveOk=true` : '') + '&useNewUrlParser=true&useUnifiedTopology=true&maxPoolSize=10&socketTimeoutMS=360000&connectTimeoutMS=10000&maxIdleTimeMS=300000',
		allowExtendedOperators: true,
		enableOptimisedfindOrCreate: true,
	},
	trap: {
		name: 'trap',
		connector: 'mongodb',
		url: 'mongodb://' + (DB_USERNAME ? `${DB_USERNAME}:${DB_PASSWORD}@` : '') + `${DB_HOST}/trap?authSource=${DB_AUTH}` + (DB_SET ? `&replicaSet=${DB_SET}&readPreference=primaryPreferred&slaveOk=true` : '') + '&useNewUrlParser=true&useUnifiedTopology=true&maxPoolSize=10&socketTimeoutMS=360000&connectTimeoutMS=10000&maxIdleTimeMS=300000',
		allowExtendedOperators: true,
		enableOptimisedfindOrCreate: true,
	}
}
