{"_meta": {"sources": ["loopback/common/models", "loopback/server/models", "../common/models", "./lib/common/models", "./lib/crm/models", "./models"], "mixins": ["loopback/common/mixins", "loopback/server/mixins", "../common/mixins", "./lib/common/mixins", "./lib/crm/mixins", "./mixins"]}, "User": {"dataSource": "db", "public": false}, "AccessToken": {"dataSource": "db", "public": false}, "ACL": {"dataSource": "db", "public": false}, "RoleMapping": {"dataSource": "db", "public": false}, "Role": {"dataSource": "db", "public": false}, "Service": {"dataSource": "db"}, "Callback": {"dataSource": "transient"}, "Trigger": {"dataSource": "default", "public": true}}