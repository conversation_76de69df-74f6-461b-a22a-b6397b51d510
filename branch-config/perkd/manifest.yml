# The manifest for the "trigger" service.
# Read the full specification for the "Load Balanced Web Service" type at:
#  https://aws.github.io/copilot-cli/docs/manifest/lb-web-service/

# Your service name will be used in naming your resources like log groups, ECS services, etc.
name: trigger
type: Load Balanced Web Service

# Distribute traffic to your service.
http:
  # Requests to this path will be forwarded to your service.
  # To match all requests you can use the "/" path.
  path: '/'
  # You can specify a custom health check path. The default is "/".
  healthcheck:
    path: '/health'
    success_codes: '200'
    healthy_threshold: 2    # number of consecutive health check successes required before considering an unhealthy target healthy
    unhealthy_threshold: 4  # number of consecutive health check failures required before considering a target unhealthy
    interval: 15s           # amount of time, in seconds, between health checks
    timeout: 10s            # amount of time, in seconds, during which no response from a target means a failed health check
    grace_period: 5s        # grace period within which to provide containers time to bootstrap before failed health checks count towards the maximum number of retries
  deregistration_delay: 10s  # amount of time to wait for targets to drain connections during deregistration.

# Configuration for your containers and service.
image:
  # Docker build arguments. For additional overrides: https://aws.github.io/copilot-cli/docs/manifest/lb-web-service/#image-build
  build: 
    dockerfile: Dockerfile
  # Port exposed through your container to route traffic to it.
  port: 8012

cpu: 256       # Number of CPU units for the task.
memory: 512    # Amount of memory in MiB used by the task.
count: 1       # Number of tasks that should be running in your service.
platform: linux/arm64
# Auto-scaling:
# count:
#   range: 1-10
#   cpu_percentage: 70
#   memory_percentage: 80
#   requests: 10000
#   response_time: 2s

exec: true     # Enable running commands in your container.

network:
  vpc:
    placement: 'private'

# Optional fields for more advanced use-cases.
#
variables:                    # Pass environment variables as key value pairs.
  ENV_REGION: 'us-west-2'

#secrets:                      # Pass secrets from AWS Systems Manager (SSM) Parameter Store.
#  GITHUB_TOKEN: GITHUB_TOKEN  # The key is the name of the environment variable, the value is the name of the SSM parameter.

# You can override any of the values defined above by environment.
environments:
  test:
    count: 1
    variables:
      NODE_ENV: 'test'
      REDIS_HOST: 'redis.test.perkd.local'
      REDIS_CLOUD_HOST: 'redis-11005.c1.us-west-2-2.ec2.cloud.redislabs.com'
      REDIS_CLOUD_PORT: 11005
      # STATS_HOST: 'graphite.test.perkd.local'
      METRICS_HOST: 'mission-control.test.crm.local'
      METRICS_PORT: 8125
      STATS_HOST: 'mission.test.perkd.local'
      TIMER_REDIS_HOST: 'redis-11005.c1.us-west-2-2.ec2.cloud.redislabs.com'
      TIMER_REDIS_PORT: 11005
    secrets:
      DB_HOST: /copilot/perkd/test/secrets/DB_HOST
      PERKD_SECRET_KEY: /copilot/perkd/test/secrets/PERKD_SECRET_KEY
      WATCHDOG_KEY_ID: /copilot/perkd/test/secrets/WATCHDOG_KEY_ID
      WATCHDOG_SECRET: /copilot/perkd/test/secrets/WATCHDOG_SECRET
      # DB_USERNAME: /copilot/perkd/test/secrets/DB_USERNAME
      # DB_PASSWORD: /copilot/perkd/test/secrets/DB_PASSWORD
      REDIS_USERNAME: /copilot/perkd/test/secrets/REDIS_USERNAME
      REDIS_PASSWORD: /copilot/perkd/test/secrets/REDIS_PASSWORD
      REDIS_CLOUD_USERNAME: /copilot/perkd/test/secrets/REDIS_CLOUD_USERNAME
      REDIS_CLOUD_PASSWORD: /copilot/perkd/test/secrets/REDIS_CLOUD_PASSWORD
      TIMER_REDIS_USERNAME: /copilot/perkd/test/secrets/REDIS_CLOUD_USERNAME
      TIMER_REDIS_PASSWORD: /copilot/perkd/test/secrets/REDIS_CLOUD_PASSWORD
  production:
    cpu: 256
    memory: 512
    count: 1
    variables:
      NODE_ENV: 'production'
      REDIS_HOST: 'redis.production.perkd.local'
      REDIS_CLOUD_HOST: 'redis-14832.c1.us-west-2-2.ec2.cloud.redislabs.com'
      REDIS_CLOUD_PORT: 14832
      # STATS_HOST: 'graphite.production.perkd.local'
      METRICS_HOST: 'mission-control.production.crm.local'
      METRICS_PORT: 8125
      STATS_HOST: 'mission.production.perkd.local'
      TIMER_REDIS_HOST: 'redis-14832.c1.us-west-2-2.ec2.cloud.redislabs.com'
      TIMER_REDIS_PORT: 14832
    secrets:
      PERKD_SECRET_KEY: /copilot/perkd/production/secrets/PERKD_SECRET_KEY
      WATCHDOG_KEY_ID: /copilot/perkd/production/secrets/WATCHDOG_KEY_ID
      WATCHDOG_SECRET: /copilot/perkd/production/secrets/WATCHDOG_SECRET
      DB_USERNAME: /copilot/perkd/production/secrets/DB_USERNAME
      DB_PASSWORD: /copilot/perkd/production/secrets/DB_PASSWORD
      DB_HOST: /copilot/perkd/production/secrets/DB_HOST
      DB_SET: /copilot/perkd/production/secrets/DB_SET
      REDIS_USERNAME: /copilot/perkd/production/secrets/REDIS_USERNAME
      REDIS_PASSWORD: /copilot/perkd/production/secrets/REDIS_PASSWORD
      REDIS_CLOUD_USERNAME: /copilot/perkd/production/secrets/REDIS_CLOUD_USERNAME
      REDIS_CLOUD_PASSWORD: /copilot/perkd/production/secrets/REDIS_CLOUD_PASSWORD
      TIMER_REDIS_USERNAME: /copilot/perkd/production/secrets/REDIS_CLOUD_USERNAME
      TIMER_REDIS_PASSWORD: /copilot/perkd/production/secrets/REDIS_CLOUD_PASSWORD
  
