{"restApiRoot": "/api", "host": "0.0.0.0", "port": 3012, "remoting": {"cors": false, "rest": {"normalizeHttpPath": false, "handleErrors": false, "xml": false}, "json": {"strict": false, "limit": "100kb"}, "urlencoded": {"extended": true, "limit": "100kb"}}, "service": {"name": "<PERSON><PERSON>", "domain": "trigger", "version": "1.0.0", "description": "", "appPath": "lib/", "settings": ["settings"], "dependencies": {}, "autoStart": true, "canTerminate": true, "state": {"now": 0, "text": "", "since": ""}, "multitenancy": true, "tenantCode": "", "tenants": {}}, "modules": {"metrics": {"enabled": true}, "eventbus": {"enabled": true}, "watchdog": {"enabled": true}}, "apiRequest": {"version": "1.0.0", "apiVersion": 0, "timeout": 30000, "https": false, "host": "", "port": "", "apiRoot": "/api", "multitenancy": true}}