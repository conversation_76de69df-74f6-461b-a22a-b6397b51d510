const LOCALHOST = '127.0.0.1',
	{
		NODE_ENV,
		// DB_HOST = NODE_ENV ? 'mongodb' : LOCALHOST,
		// DB_USERNAME = '',
		// DB_PASSWORD = '',
		DB_HOST = '************',
		DB_USERNAME = 'admin',
		DB_PASSWORD = 'Perkd100_CRM_test',
		DB_AUTH = 'admin',
		DB_SET = '',
	} = process.env

module.exports = {
	trap: {
		name: 'trap',
		connector: 'mongodb',
		url: 'mongodb://' + (DB_USERNAME ? `${DB_USERNAME}:${DB_PASSWORD}@` : '') + `${DB_HOST}/trap?authSource=${DB_AUTH}` + (DB_SET ? `&replicaSet=${DB_SET}&readPreference=primaryPreferred&slaveOk=true` : '') + '&useNewUrlParser=true&useUnifiedTopology=true&keepAlive=false&maxPoolSize=3&socketTimeoutMS=360000&connectTimeoutMS=10000&maxIdleTimeMS=300000',
		allowExtendedOperators: true,
		enableOptimisedfindOrCreate: true,
	}
}
