#!/bin/bash
echo "COMPOSE BRANCH SCRIPT"

app=$1
service=$2
rules=$3
if [[ -z "$app" ]]; then
    echo "crm or perkd?"
    read app
fi
if [[ -z "$service" ]]; then
    echo "service name: "
    read service
fi

if [ -d "copilot/${service}" ]; then
  echo ""
else
  mkdir "copilot/${service}"
fi

# copilot files
ln -s -F ../branch-config/$app/.workspace ./copilot/.workspace
ln -s -F ../../branch-config/$app/manifest.yml ./copilot/$service/manifest.yml

# git submodule
ln -s -F ../branch-config/$app/model-config.json ./server/model-config.json

# CONFIGS
ln -s -F ../branch-config/$app/config.json ./server/config.json
ln -s -F ../branch-config/$app/datasources.local.js ./server/datasources.local.js
ln -s -F ../../branch-config/$app/eventbus.json ./server/config/eventbus.json
ln -s -F ../../branch-config/$app/metrics.json ./server/config/metrics.json

# rules
ln -s -F ./server/lib/common/.$rules ./.$rules
ln -s -F ./branch-config/$app/.clinerules ./.clinerules
ln -s -F ./branch-config/$app/.cursorrules ./.cursorrules

# Dockerfile
ln -s -F ./branch-config/$app/Dockerfile ./Dockerfile

echo "Done, setting up branch for $app"