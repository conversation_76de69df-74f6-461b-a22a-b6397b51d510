# The manifest for the "pipeline-crm-trigger" pipeline.
# This YAML file defines your pipeline: the source repository it tracks and the order of the environments to deploy to.
# For more info: https://aws.github.io/copilot-cli/docs/manifest/pipeline/

# The name of the pipeline.
name: pipeline-crm-trigger
# The version of the schema used in this template.
version: 1
# This section defines your source, changes to which trigger your pipeline.
source:
  # The name of the provider that is used to store the source artifacts.
  # (i.e. GitHub, Bitbucket, CodeCommit)
  provider: GitHub
  # Additional properties that further specify the location of the artifacts.
  properties:
    branch: main
    repository: https://github.com/perkd/dual-trigger.git
    # Optional: specify the name of an existing CodeStar Connections connection.
    connection_name: copilot-github-waveo
build:
  image: aws/codebuild/amazonlinux2-aarch64-standard:3.0
# This section defines the order of the environments your pipeline will deploy to.
stages:
  - # The name of the environment.
    name: test
    # Optional: flag for manual approval action before deployment.
    # requires_approval: true
    # Optional: use test commands to validate this stage of your build.
    # test_commands: [echo 'running tests', make test]
  - # The name of the environment.
    name: production
    # Optional: flag for manual approval action before deployment.
    requires_approval: true
    # Optional: use test commands to validate this stage of your build.
    # test_commands: [echo 'running tests', make test]
