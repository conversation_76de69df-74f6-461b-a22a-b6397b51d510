/**
 * @module Service:Trigger
 */

const { Triggers } = require('@crm/types'),
	{ Context } = require('@perkd/multitenant-context'),
	{ Timers, Events } = require('@perkd/timers'),
	{ bm } = require('@perkd/utils'),
	Service = appRequire('lib/common/Service')

const { State } = Triggers,
	EVENT = { // IMPORTANT:  Event is extended by various modules (in their constructor) incrementally
		TRIGGER: {
			SCHEDULED: 'trigger.scheduled',
			RUNNING: 'trigger.running',
			COMPLETED: 'trigger.completed',
			ENDED: 'trigger.ended',
			FAILED: 'trigger.failed',

			TIMER: 'trigger.timer',
			EVENT: 'trigger.event',
			REQUEST: 'trigger.request',
		},
	},
	{ SCHEDULED, ENDED, FAILED } = Events,		// timer
	{ time: now } = bm

class TriggerService extends Service {

	constructor(app, service) {
		super(app, service)

		// ---  app Events handlers

		app.on(EVENT.TRIGGER.FAILED, e => {
			console.error('Trigger.event.failed', now() + '  Trigger failed (id: ' + e.id + ') - ' + e.reason + ' ' + JSON.stringify(e.error))
			app.emit('watchdog.error', [ 'Failed', e ])
		})
	}

	async _ready() {
		const { app } = this,
			{ models } = app,
			{ Trigger } = models,
			{ watchdog } = app.Service,
			allTenants = app.allTenantCodes(),
			list = []

		this.timer = new Timers('trigger')

		// Pre-initialize connection pool for tenants before running operations
		if (app.connectionManager) {
			const tenants = app.allTenantCodes()
			// Create connection pools in parallel with concurrency control
			await Promise.all(tenants.map(async tenant => {
				try {
					await app.connectionManager.ensureConnection(tenant)
					console.log(`Pre-initialized connection pool for ${tenant}`)
				}
				catch (err) {
					console.error(`Failed to pre-initialize pool for ${tenant}:`, err)
				}
			}))
		}

		this.timer.on(SCHEDULED, (job, tenant) => {
			const { id, delay, repeat = {} } = job,
				{ count = 1, limit } = repeat,
				nextRunAt = new Date(Date.now() + delay),
				repeatRemaining = limit ? limit - count : undefined

			if (count && count > 1) {
				Context.runAsTenant(tenant, async () => {
					const trigger = await Trigger.findById(id)

					if (!trigger) return
					await trigger.updateAttributes({ repeatRemaining, nextRunAt }).catch()
				}, app.connectionManager).catch(err => {
					console.log('Error [SCHEDULED] %j', err)
				})
			}
		})

		this.timer.on(ENDED, (job, tenant) => {
			const { id } = job,
				state = State.ENDED

			Context.runAsTenant(tenant, async () => {
				const trigger = await Trigger.findById(id)

				if (!trigger) return
				await trigger.updateAttributes({ state, nextRunAt: null }).catch()
			}, app.connectionManager).catch()
		})

		this.timer.on(FAILED, (job, error, tenant) => {
			const { id } = job,
				state = State.FAILED,
				failedAt = new Date(),
				failReason = error

			Context.runAsTenant(tenant, async () => {
				const trigger = await Trigger.findById(id)

				if (!trigger) return
				await trigger.updateAttributes({ state, failedAt, failReason }).catch()
			}, app.connectionManager).catch()
		})

		for (const tenant of allTenants) {
			list.push(
				await Context.runAsTenant(tenant, context => Trigger.houseKeep(tenant, context), app.connectionManager)
			)
		}

		const receipts = list.filter(r => !!r)
		app.echo(`✅ housekeep completed (${receipts.length})`, JSON.stringify(receipts))
		watchdog.log('trigger.housekeep', { receipts })
	}

	async _start() {
		const { app, timer } = this,
			{ models } = app,
			{ Trigger } = models,
			allTenants = app.allTenantCodes(),
			list = []

		try {
			for (const tenant of allTenants) {
				list.push(
					await Context.runAsTenant(tenant, () => Trigger.restore(tenant), app.connectionManager)
				)
			}

			const receipts = list.filter(r => !!r)
			app.echo(`✅ restore completed for ${receipts.length} tenants`)

			await timer.start()
		}
		catch (error) {
			appNotify('trigger.start', error, 'error')
		}
	}

	async _stop() {
		const { app, timer } = this

		try {
			app.Service.eventbus.unsubAllTenants()		// stop all event listeners
			return timer.stop()
		}
		catch (error) {
			appNotify('trigger.stop', error, 'error')
		}
	}

	async _pause() {
		const { app, timer } = this

		try {
			app.Service.eventbus.pause()
			return timer.stop()
		}
		catch (error) {
			appNotify('trigger.pause', error, 'error')
		}
	}

	async _resume() {
		const { app, timer } = this

		try {
			app.Service.eventbus.resume()
			return timer.start()
		}
		catch (error) {
			appNotify('trigger.resume', error, 'error')
		}
	}

	async _terminate() {
		const { app, timer } = this,
			{ Metric } = app

		// reset metric (gauge)
		appMetric(Metric.execute.total, '0', { child: Metric.child.request })

		try {
			return timer.end()
		}
		catch (error) {
			appNotify('trigger.terminate', error, 'error')
		}
	}
}

module.exports = exports = TriggerService
exports.Event = Event
