{"name": "<PERSON><PERSON>", "plural": "Triggers", "description": "", "base": "PersistedModel", "idInjection": true, "strict": false, "options": {"validateUpsert": true}, "mixins": {"Multitenant": true, "Common": true, "Errors": true, "Request": true, "Callback": {"limit": 100, "interval": 1000, "useSettings": false}, "Event": true, "Timer": true}, "properties": {"name": {"type": "String", "required": true, "max": 128}, "description": {"type": "String", "max": 256}, "type": {"type": "String", "enum": ["timer", "event", "request"]}, "priority": {"type": "Number", "max": 20}, "callback": {"type": "Callback"}, "start": {"type": "Date"}, "end": {"type": "Date"}, "persistAfterEnd": {"type": "Boolean", "default": false}, "events": ["String"], "condition": {"type": "String"}, "repeatInterval": {"type": "Any", "description": "String or Number"}, "repeat": {"type": "Number"}, "repeatRemaining": {"type": "Number"}, "timezone": {"type": "String", "description": "moment-timezone, used by Agenda when using an interval in the cron string format"}, "state": {"type": "String", "enum": ["scheduled", "running", "paused", "completed", "failed", "ended"]}, "active": {"type": "Boolean", "default": true}, "nextRunAt": {"type": "Date"}, "lastRunAt": {"type": "Date"}, "lastFinishedAt": {"type": "Date"}, "failedAt": {"type": "Date"}, "failReason": {"type": "Object"}, "visible": {"type": "boolean", "default": true}, "createdAt": {"type": "date"}, "modifiedAt": {"type": "date"}}, "validations": [], "relations": {}, "acls": [], "scopes": {}, "indexes": {"name_index": {"keys": {"name": 1}}}, "methods": {"prototype.schedule": {"description": "Set start of Timer trigger", "http": {"path": "/schedule", "verb": "post"}, "accepts": [{"arg": "when", "type": "any"}], "returns": {"type": "object", "root": true}}, "prototype.listen": {"description": "Set Event trigger to listen to event(s)", "http": {"path": "/listen", "verb": "post"}, "accepts": [{"arg": "events", "type": "array", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.handle": {"description": "Set Request trigger to handle form posts", "http": {"path": "/handle", "verb": "post"}, "returns": {"type": "object", "root": true}}, "prototype.request": {"description": "Accepts request from Event Gateway", "http": {"path": "/request", "verb": "post"}, "accepts": [{"arg": "param", "type": "object", "http": {"source": "body"}, "required": true}], "returns": {"type": "object", "root": true}}, "prototype.pause": {"description": "Pause trigger immediately", "http": {"path": "/pause", "verb": "post"}, "accepts": [{"arg": "when", "type": "string"}], "returns": {"type": "object", "root": true}}, "prototype.resume": {"description": "Resume trigger immediately", "http": {"path": "/resume", "verb": "post"}, "accepts": [{"arg": "when", "type": "string"}], "returns": {"type": "object", "root": true}}, "prototype.stop": {"description": "Stop trigger immediately", "http": {"path": "/stop", "verb": "post"}, "returns": {"type": "object", "root": true}}}}