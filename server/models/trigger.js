/**
 *  @module Model:Trigger
 *
 *  Usage of properties:
 * 		repeatInterval	 (Timer only) milli-seconds or cron format or human-interval.  undefined & null => only ONCE, non-recurring
 */
const { Triggers } = require('@crm/types'),
	{ bm, satisfyCondition, operations } = require('@perkd/utils')

const { State, Type } = Triggers,
	{ SCHEDULED, RUNNING, PAUSED, COMPLETED, FAILED, ENDED } = State,
	{ TIMER } = Type

module.exports = function(Trigger) {

	// -----  Static Methods  -----

	Trigger.restore = async function(tenant) {
		const [ events, timers ] = await Promise.all([
			Trigger.restoreEventHandlers(tenant),
			Trigger.restoreTimerHandlers(tenant),
		])

		return events || timers ? { [tenant]: { events, timers } } : undefined
	}

	/**
	 * Remove expired triggers
	 * 		1. remove expired (non-persist) triggers
	 * 		2. remove any orphaned job (ie. missing trigger)
	 * @param {String} tenant
	 * @return {Promise<Object|null>} { [tenant]: { triggers: 'deleted X triggers', timers: ['removed orphaned timer: xxxx'] }}
	 */
	Trigger.houseKeep = async function(ctx) {
		let retries = 5
		let baseDelay = 500 // 0.5s initial delay
		try {
			await new Promise(resolve => {
				if (this.app.connectionManager) return resolve()
				this.app.once('connectionManagerReady', resolve)
			})

			while (retries-- > 0) {
				try {
					const ds = this.getDataSource()
					return await this.destroyAll({ expireAt: { lt: new Date() } })
				}
				catch (err) {
					if (err.code === 'POOL_INITIALIZING' && retries > 0) {
						// Exponential backoff with jitter
						const delay = baseDelay * Math.pow(2, 5 - retries) + Math.random() * 200
						await new Promise(r => setTimeout(r, delay))
						continue
					}
					throw err
				}
			}
			throw new Error('Max retries reached for connection pool initialization')
		}
		catch (err) {
			console.error('Housekeeping failed:', err)
			throw err
		}
	}

	// -----  Instance Methods  -----

	Trigger.prototype.pause = async function() {
		const { id, state, type } = this,
			{ app } = Trigger,
			{ Event, Service } = app,
			{ timer: Timer } = Service.main

		if (![ SCHEDULED, COMPLETED, FAILED ].includes(state)) {
			throw new Error(`Unable to pause (state: ${state})`)
		}

		if (type === TIMER) await Timer.pause(id)
			.catch(err => {
				console.error('Error pausing timer', err)
			})

		await this.updateAttributes({ state: PAUSED, active: false })
		app.emit(Event.trigger.PAUSED, { id, type })
		return this
	}

	Trigger.prototype.resume = async function() {
		const { id, state, type } = this,
			{ app } = Trigger,
			{ Event, Service } = app,
			{ timer: Timer } = Service.main

		if (![ PAUSED, FAILED ].includes(state)) {
			throw new Error(`Unable to resume (state: ${state})`)
		}

		if (type === TIMER) await Timer.resume(id)
			.catch(err => {
				console.error('Error resuming timer', err)
			})

		await this.updateAttributes({ state: SCHEDULED, active: true })
		app.emit(Event.trigger.RESUMED, { id, type })
		return this
	}

	Trigger.prototype.stop = async function() {
		const { id, type } = this,
			{ app } = Trigger,
			{ Event } = app,
			changes = { state: ENDED, active: false, nextRunAt: null }

		if (this.type === TIMER) await this.cancelTimers()

		await this.updateAttributes(changes)
		app.emit(Event.trigger.ENDED, { id, type })
		return this
	}

	/**
	 * Make remote call to Callback
	 * @param  {Object} data - payload to pass to Callback
	 * @return {Object} { result, changes }
	 */
	Trigger.prototype.remoteCallback = async function(data) {
		const { id, start, end, state, active, repeatInterval, repeatRemaining, nextRunAt } = this,
			{ app } = Trigger,
			{ Event, Metric } = app,
			NOW = new Date()

		//  check to ensure only fire between start/end time AND not ENDED AND is active
		if (state === PAUSED || start > NOW) { 	// BEFORE start (time) OR paused
			const err = {
				status: 404,
				message: start > NOW ? 'Trigger not started' : 'Trigger is paused',
			}
			throw err
		}

		if (!active || (state === ENDED) || (end && end < NOW)) {
			//  AFTER end (time) OR state == ENDED OR  not active
			await this.updateAttributes({ state: ENDED, active: false, nextRunAt: null })

			const err = {
				status: 404,
				message: state === ENDED ? 'Trigger has ended' : 'Trigger not active',
			}
			throw err
		}

		// -- update states
		const _start = bm.mark(),
			isLastRun = (repeatInterval === 0) || (repeatRemaining === 0),
			updates = {
				lastRunAt: NOW,
				nextRunAt: isLastRun ? null : nextRunAt,
				state: RUNNING,
			}

		await this.updateAttributes(updates)
		app.emit(Event.trigger.RUNNING, { id })

		let changes, result
		try {
			result = await this.doCallback(data)
			changes = isLastRun ? { state: ENDED, active: false } : { state: COMPLETED }

			app.emit(Event.trigger.COMPLETED, { id, nextRunAt })
			appMetric(Metric.execute.latency, bm.diff(_start, bm.mark()), { child: Metric.child.request })
		}
		catch (error) {
			result = null
			changes = {
				state: FAILED,
				failedAt: new Date(),
				failReason: { error },
			}

			app.emit(Event.trigger.FAILED, { id, error, reason: null })
		}

		changes.lastFinishedAt = new Date()
		await this.updateAttributes(changes)
		return result
	}

	// -----  Private Methods  -----

	/**
	 * Prepare for remote Callback & handle post-call clean up of trigger & timer (if necessary)
	 * @param {Trigger} trigger instance
	 * @param {Object} options
	 * 			{Object} data: obj used to test condition
	 *			{Object} param: data/parameters received from timer/event/request, will extend with payload if exist
	 * @return {Promise<Object|void>}
	 */
	Trigger._doCallback = async function(trigger, options = {}) {
		const { id, condition } = trigger,
			{ app } = Trigger,
			{ Metric } = app,
			{ data, param = {} } = options,
			child = Metric.child.request

		if (!data || satisfyCondition(data, condition, { operations })) {
			appMetric(Metric.execute.total, '+1', { child })

			await trigger.remoteCallback({ ...param, _triggerId: String(id) }).catch(err => null)

			appMetric(Metric.execute.total, '-1', { child })

			const { state, persistAfterEnd } = trigger

			// post-call cleanup, destroy trigger & timers no longer needed
			if (state === ENDED) {
				if (!persistAfterEnd) trigger.destroy()
			}
		}
		// does NOT satisfy conditions, just return
	}

	// -----  Validations  -----

	function _validateName(name) {
		if (!name) return new Error('`name` missing')
		return null
	}

	function _validateCallback(hook) {
		if (!hook) return new Error('`callback` missing, eg: callback: { method: "post", url: "api.domain.com" }')

		hook.method = _validateHttpMethod(hook)

		if (!hook.url) return new Error('`url` missing in `callback`, eg: { method: "post", url: "api.domain.com" }')
		return null
	}

	function _validateHttpMethod(hook) {
		const method = hook.method || 'post'		// default to POST
		return ([ 'post', 'get', 'put', 'delete' ].indexOf(method) === -1) ? 'post' : method
	}

	// -----  Remote & Operation hooks  -----

	Trigger.observe('before save', async ({ isNewInstance, instance, data, currentInstance }) => {
		const NOW = new Date()

		let err
		if (isNewInstance) {	// Create - new instance
			err = _validateName(instance.name)

			if (!err) err = _validateCallback(instance.callback)
			instance.createdAt = NOW
		}
		else {					// Update existing instance
			const updated = instance || data,
				{ callback } = updated

			if (instance) {		//  saving entire instance
				err = _validateCallback(callback)
			}
			else if (callback) { //  (partial) updateAttributes
				err = _validateCallback(callback)
			}

			updated.modifiedAt = NOW
		}

		return err
	})

	Trigger.observe('before delete', async ({ instance, where }) => {
		const trigger = instance || await Trigger.findOne({ where })

		if (trigger) {
			trigger.cancelTimers().catch(() => null)
		}
	})
}
