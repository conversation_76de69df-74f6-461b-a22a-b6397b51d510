//  Module Dependencies
var inherits = require('util').inherits,
	EventEmitter = require('eventemitter2').EventEmitter2;

var FRAME = 60;			// 60x 1 second slots = 1 minute
var INTERVAL = 1000;	// time between emitting 'speed' events, in milliseconds
var AVGSECOND = 5;		// past 5 seconds for Per Second speed average

var Event = {
	speed: 'event.speed'
};

var time = {
	start: process.hrtime(),
	values: 0,		// number of values in timeSeries array, used for accurate speed calculation before timeSeries array fully filled
	event: {},
	lastEmit: process.hrtime(),
	emitting: false,
	pending: 0
};


function EventSpeed(param) {
	if (!(this instanceof EventSpeed)) {
		return new EventSpeed(param);
	}
	param = param || {};

	FRAME = param.frame || FRAME;
	INTERVAL = param.interval || INTERVAL;
	AVGSECOND = param.avgSecond || AVGSECOND;
}

inherits(EventSpeed, EventEmitter);
module.exports = exports = EventSpeed;


//-----  Instance Methods  -----

EventSpeed.prototype.count = function(evtName) {
	var self = this;

	count(evtName);

	if (!time.emitting) {
		var lapsed = process.hrtime(time.lastEmit);
		if (((lapsed[0] * 1000) + (lapsed[1]) / 1000000) >= INTERVAL) {
			self.emitEvent(evtName);
		}
		time.emitting = setTimeout(function() {
			time.emitting = false;
			if (time.pending > 0) {
				self.emitEvent(evtName);
			}
			time.pending = 0;
		}, INTERVAL);
	} else {
		time.pending++;
	}
};

EventSpeed.prototype.clear = function(evtName) {
	var self = this;

	if (time.event[evtName])	delete time.event[evtName];
};

EventSpeed.prototype.emitEvent = function(evtName) {
	var self = this;

	time.lastEmit = process.hrtime();
	self.emit(Event.speed, {
		name: evtName,
		minute: aggregate(time.event[evtName], time.values).total.toFixed(),
		second: time.event[evtName][time.values - 2],	// use previous 1 second (-1), current second is incomplete (is array index, another -1)
		avgSecond:  aggregate(time.event[evtName], -AVGSECOND).average.toFixed()
	});
};

//-----  Private Methods  -----

function count(key) {
	var now = process.hrtime();
	var ndx = process.hrtime(time.start)[0];
	time.event[key] = time.event[key] || [];

	if (ndx >= (2 * FRAME) - 1) {
		time.start = now;
		time.event[key] = [];
		ndx = 0;

	} else if (ndx > FRAME - 1) {
		var diff = ndx - (FRAME - 1);
		time.start[0] += diff;
		time.event[key] = time.event[key].slice(diff);

		if (diff == 1) {
			time.event[key].push(0);
		} else {
			var avg = aggregate(time.event[key]).average;
			for (var i = 0; i < diff; i++) {
				time.event[key].push(avg);
			}
			time.event[key][FRAME-1] = 0;
		}
		ndx = FRAME - 1;
	}

	var gap = ndx - time.event[key].length + 1;
	if (gap > 0) {
		for (var i = 0; i < gap; i++ ) {
			time.event[key].push(0);
		}
	}

	time.event[key][ndx]++;
	time.values = ndx + 1;
}

/**
 * Aggregate Total & Average of an array
 * @param  {Array} timeSeries 	array of values
 * @param  {Number} limit  		(optional) number of values from start of array, negative limit => from end of array backwards
 * @return {Object}      		{ total, average }
 */
function aggregate(timeSeries, limit) {
	var total = 0;
	limit = limit || timeSeries.length;
	if (limit >= 0) {
		var start = 0;
		var end = (limit <= timeSeries.length) ? limit : timeSeries.length;
	} else {
		var start = (Math.abs(limit) <= timeSeries.length) ? (timeSeries.length + limit) : 0;
		var end = timeSeries.length - 1;
	}
	for (var k = start; k < end; k++) {
		total += timeSeries[k];
	}
	return { total: total, average: total / (end - start)};
}
