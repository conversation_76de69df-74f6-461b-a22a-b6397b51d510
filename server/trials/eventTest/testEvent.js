
const
	_ = require('lodash'),
	Test = require('../timerTest/Test'),
	redis = require('redis'),
	shortid = require('shortid'),
	pubClient = redis.createClient(6379, '127.0.0.1'),
	Trigger = require('../timerTest/triggerLib'),
	argv = require('minimist')(process.argv.slice(2));

const
	CONTEXT = {
		tenant: 'dev_young',
		db: {
			host: 'localhost',
			name: '__tests',
		},
		schema: {
			type: String,
			tenant: String,
			triggerId: String,
			events: Array,
			error: String,
			lag: Number,
			payload: {
				tenant: String,
				timestamp: Number,
				scheduled: Number,
			},
		},
		trigger: {
			host: 'localhost',
			path: '/api/Triggers',
		},
	},
	DEFAULT = {
		tenant: 'dev_young',
		port: 3000,
		// start: Date.now(),
		// end: Date.now(),
		// repeat: 1,
		repeatInterval: null,
		events: ['a.b.c'],
		// persistAfterEnd: false
	};

class Event extends Test {
	generateEvents(param) {
		const settings = Object.assign({}, DEFAULT, param);
		return this.createEvent(settings).then(result => {
			result.events = settings.events;
			showParams(settings);
			return this.listenEvent(result);
		});
	}

	fireEvent(param) {
		let TOTAL = param.count || 100;
		const
			event = param.events[0],
			parts = event.split('.');

		console.log('\n>>> connected to eventbus <<<\n');
		const interval = setInterval(() => {
			publish(param.tenant, parts[0], parts[1], parts[2]);
			this.model.create({
				type: 'fireEvent',
				tenant: param.tenant,
				triggerId: param.triggerId,
				when: new Date().getTime(),
				payload: {
					tenant: param.tenant,
					triggerId: param.triggerId,
					timestamp: param.timestamp,
					scheduled: new Date().getTime(),
				},
				events: [event],
			});

			if (!--TOTAL) {
				clearInterval(interval);
				console.log('\n>>> All published <<<\n');
			}
		}, 1000); // delay 10ms

		function publish(tenant, domain, actor, action) {
			const channel = tenant + '.' + event;
			const message = JSON.stringify({
				id: shortid.generate(), // event uuid
				tenantCode: tenant,
				domain: domain,
				actor: actor,
				action: action,
				data: {},
				published: Date.now(), // the number of milliseconds elapsed since 1 January 1970 00:00:00 UTC
			});
			pubClient.publish(channel, message);
		}
	}
}

function showParams(param) {
	console.log('\n---- [%s] event (callback at port:%s) starting %s:', param.tenant, param.port, param.events);
	const opts = Object.assign({}, param);
	delete opts.tenant;
	delete opts.port;
	delete opts.events;
	Object.keys(opts).forEach(p => {
		console.log('  %s:  %s', p, param[p]);
	});
	console.log('\n');
}

Event.prototype.createEvent = Trigger.createEvent;
Event.prototype.listenEvent = Trigger.listenEvent;

testTenant(getParams(argv));

function getParams(argv) {
	return _.pick(argv, ['tenant', 'port', 'start', 'end', 'repeat', 'repeatInterval', 'persistAfterEnd', 'events']);
}

function testTenant(param) {
	const tenant = param.tenant ? { tenant: param.tenant } : {},
		port = param.port ? { server: { port: param.port }} : {},
		test = new Event(Object.assign({}, CONTEXT, port, tenant));

	test.on('callback', function(evt) {
		this.model.create({
			type: 'callback',
			tenant: this.tenant,
			triggerId: evt.payload.triggerId,
			events: evt.payload.events,
			error: (this.tenant !== evt.payload.tenant) ? 'Mismatch tenant' : undefined,
			lag: Date.now() - evt.payload.scheduled,
			payload: evt.payload,
		});
		console.log('[%s]  ', this.tenant, evt.payload.timestamp);
	});

	test.on('ready', () => {
		test.generateEvents(param).then(result => {
			test.fireEvent(result);
		});
	});
}
