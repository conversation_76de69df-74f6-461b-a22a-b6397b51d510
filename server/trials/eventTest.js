
var	humanInterval = require('human-interval'),
	bm = require('../lib/common/benchmark'),
	Trigger = require('../lib/clients/triggerClient');

var redis = require('redis'),
	eventbus = redis.createClient(6379, 'eventbus.perkd.intranet');

var accessToken = 'eyJhbGciOiJIUzI1NiJ9.eyJ0ZW5hbnQiOnsiY29kZSI6ImRldl95b3VuZyJ9fQ.OfsXQeV22jIbCMbb99v4LsOb39ZxGabNZGh4PouRLtY';

var max = 10;
var interval = 3000;
var count = 1;

var events = [];
var startTime = '10 seconds';
var endTime = '20 seconds';

var now = Date.now();

// Event trigger
var id = '122334';
var trigger = new Trigger({
	name: "Event trigger (" + bm.now() + ")",
	start: new Date(now + humanInterval(startTime)),
	end: new Date(now + humanInterval(endTime)),
	webhook: {
		method: "POST",
		url: "http://127.0.0.1:8013/api/Campaigns/" + id + "/start",
		payload: {
			p1: 'event trigger: 123888',
		}
	},
	events: [ 'campaign.evt1', 'campaign.evt2' ],
	repeat: 5
});


trigger.create().then(function(obj){
	console.log('[create]  id: %s (%s)', trigger.id, JSON.stringify(obj));
	return trigger.listen(events, { data: {xx: 1234}});
	})
.then(function(res) {
	console.log('[listen]  Event trigger ready, listening: %s', JSON.stringify(trigger.events));
	console.log('          start: %s    end: %s', trigger.start, trigger.end);


	fireEvents();

	})
.fail(function(err) {
	console.log('Error: %s `%s` (%s)', err.status, err.message, JSON.stringify(err.details));
	})
.done();


function emitOne(evt) {
	eventbus.publish(evt, JSON.stringify({ id: count++, bmStart: bm.now() }));
	console.log('%s  Emit event: `%s`', bm.time(), evt);
}

function fireEvents() {
	console.log('------ Running: %s times at %s ms interval -----', max, interval);
	var timer = setInterval(function(){
		emitOne('campaign.evt1');

		if (count > max) {
			clearTimeout(timer);
			console.log('-----------------\n');
		}
	}, interval);
}
