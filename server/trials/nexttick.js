
console.log('\nCallback without nextTick()');
testNextTick(false);

console.log('\nCallback WITH nextTick()');
testNextTick(true);

console.log('\nUsing Promise');
testPromise();

function testNextTick(next) {
	const tag = next ? 'with' : 'without';
	function tick(cb) {
		console.log('[%s]    1. start', tag);
		if (next)  process.nextTick(cb);
		else cb();
	}
	tick(() => {
		console.log('[%s]    2. callback', tag);
	});
	console.log('[%s]    3. ended', tag);
}

function testPromise() {
	function prom() {
		console.log('[promise] 1. start');
		return new Promise((resolve, reject) => {
			resolve();
		});
	}
	prom().then(() => {
		console.log('[promise] 2. resolved');
	});

	console.log('[promise] 3. ended');
}
