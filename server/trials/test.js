
var date = require('date.js');
var querystring = require('qs');

var data = {
	start: new Date(),
	end: 'in 10 seconds'
};

// console.log('start: %s', data.start);

// var qs = JSON.stringify(data);
// console.log('data: %s', qs);

// var param = JSON.parse(qs);
// console.log('start(2): %s (%s)', param.start, typeof param.start);

// var time = date(param.start);
// console.log('start(3): %s (%s)', time, typeof time);

// var time2 = new Date(param.start);
// console.log('start(4): %s (%s)', time2, typeof time2);

// var time3 = Date.parse('xxxxxx');

// if (!time3) {
// 	console.log('@@@@@@');
// } else {
// 	console.log('start(5): %s (%s)', time3, typeof time3);
// }

var param = {};	//'2016/12/12'
var startTime = undefined;

var time = Date.parse(param.when);
console.log('time is valid: %s', !isNaN(time));

startTime = startTime || (isNaN(time) ? null : new Date(time)) || new Date();
console.log('startTime: %s', startTime);


