//  Module Dependencies
var EventEmitter = require('eventemitter2').EventEmitter2,
	bm = require('../lib/common/benchmark');

var FRAME = 60;			// 60x 1 second slots = 1 minute
var INTERVAL = 1000;	// time between emitting 'speed' events, in milliseconds

var time = {
	start: process.hrtime(),
	values: 0,		// number of values in timeSeries array, used for accurate speed calculation before timeSeries array fully filled
	event: {},
	lastEmit: process.hrtime(),
	emitting: false,
	pending: 0
};

var EVENT = 'x.card.add';


console.log('--- Start: ', time.event);

var timer;

setTimeout(function() {
	run();
	stopAfter(150000);
}, 3000);


function run() {
	timer = setInterval(function() {
		var rand = (Math.random() * 100).toFixed();
		for (var i = 0; i < 10; i++) {
			count(EVENT);
		}
		if (!time.emitting) {
			var lapsed = process.hrtime(time.lastEmit);
			if (((lapsed[0] * 1000) + (lapsed[1]) / 1000000) >= INTERVAL) {
				emitEvent();
			}
			time.emitting = setTimeout(function() {
				time.emitting = false;
				if (time.pending > 0)   emitEvent();
				time.pending = 0;
			}, INTERVAL);
		} else {
			time.pending++;
		}
	}, 100);
}


function stopAfter(duration) {
	setTimeout(function() {
		clearInterval(timer);
		console.log('event: %s', JSON.stringify(time.event));
	}, duration);
}


function emitEvent() {
	time.lastEmit = process.hrtime();
	var perMinute = aggregate(time.event[EVENT], time.values).total.toFixed();
	var perSecond = time.event[EVENT][time.values - 2];
	var avg5Sec = aggregate(time.event[EVENT], -5).average.toFixed();
	console.log('speed: %s per minute \tper second: %s  (5sec avg: %s)', perMinute, perSecond, avg5Sec);
	// console.log(JSON.stringify(time.event[EVENT]));
}

function count(event) {
	var now = process.hrtime();
	var ndx = process.hrtime(time.start)[0];
	time.event[event] = time.event[event] || [];

	if (ndx >= (2 * FRAME) - 1) {
		time.start = now;
		time.event[event] = [];
		ndx = 0;

	} else if (ndx > FRAME - 1) {
		var diff = ndx - (FRAME - 1);
		time.start[0] += diff;
		time.event[event] = time.event[event].slice(diff);

		if (diff == 1) {
			time.event[event].push(0);
		} else {
			var avg = aggregate(time.event[event]).average;
			for (var i = 0; i < diff; i++) {
				time.event[event].push(avg);
			}
			time.event[event][FRAME-1] = 0;
		}
		ndx = FRAME - 1;
	}

	var gap = ndx - time.event[event].length + 1;
	if (gap > 0) {
		for (var i = 0; i < gap; i++ ) {
			time.event[event].push(0);
		}
	}

	time.event[event][ndx]++;
	time.values = ndx + 1;
}


/**
 * Aggregate Total & Average of an array
 * @param  {Array} timeSeries 	array of values
 * @param  {Number} limit  		(optional) number of values from start of array, negative limit => from end of array backwards
 * @return {Object}      		{ total, average }
 */
function aggregate(timeSeries, limit) {
	var total = 0;
	limit = limit || timeSeries.length;
	if (limit >= 0) {
		var start = 0;
		var end = (limit <= timeSeries.length) ? limit : timeSeries.length;
	} else {
		var start = (Math.abs(limit) <= timeSeries.length) ? (timeSeries.length + limit) : 0;
		var end = timeSeries.length - 1;
	}
	for (var k = start; k < end; k++) {
		total += timeSeries[k];
	}
	return { total: total, average: total / (end - start)};
}

