
var redis = require("redis");

// var EVENTBUS = 'eventbus.perkd.intranet';		// 1.38
var EVENTBUS = 'odin3.waveo.com';				// Dev

var pubber = redis.createClient(6379, EVENTBUS);

var tenant = 'dev_wenyan';

var domain = 'trigger';
var event = 'trigger.test.evt1';
var data = { delay: 3000 };

// console.log('Event: [%s]  ', event, data);

_doPublish(tenant, domain, event, data);

// batch(10000, 100);

function batch(total, interval) {
	var i = 0;
	var tenants = ['dev_young', 'dev_wenyan', 'dev_wilson'];
	var count = [0, 0, 0];

	var int = setInterval(function() {
		i++;
		console.log('total:', i);
		var ndx = Math.floor(Math.random() * tenants.length);
		count[ndx]++;

		_doPublish(tenants[ndx], domain, event, data);

		if (i >= total) {
			clearInterval(int);
			var k = 0;
			tenants.forEach(function(tenant) {
				console.log('[%s] \t%s events', tenant, count[k++]);
			});
		}
	}, interval);
}

function _doPublish(tenantCode, domain, name, data) {
	pubber.publish(tenantCode+'.'+name, JSON.stringify({
		tenantCode: tenantCode,
		domain: domain,
		id: Date.now(),
		name: name,
		data: data,
		published: Date.now()
	}));
}
