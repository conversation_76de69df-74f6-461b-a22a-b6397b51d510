
/**
	ONLY works with Test.js (sub-classes)
**/

const
	chai = require('chai'),
	chaiHttp = require('chai-http'),
	helper = require('../../lib/common/helper');

chai.use(chaiHttp);

const
	TRIGGER_PORT = 8012;

function createTimer(param) {
	const timestamp = Date.now();
	param = param || {};

	return chai.request('http://' + this.ctx.trigger.host + ':' + TRIGGER_PORT)
		.post(this.ctx.trigger.path)
		.set('tenant-code', this.tenant)
		.send({
			name: 'Test: timer #' + process.hrtime()[1],		// nanoseconds
			type: 'timer',
			callback: this.ctx.callback,
			start: param.start || new Date(),
			end: param.end || undefined,
			persistAfterEnd: param.persistAfterEnd || undefined,
			repeat: param.repeat || undefined,
			repeatInterval: param.repeatInterval || undefined,
		})
		.then(res => {
			return { tenant: this.tenant, triggerId: res.body.id, timestamp: timestamp };
		});
}

function createEvent(param) {
	const timestamp = Date.now();
	param = param || {};

	return chai.request('http://' + this.ctx.trigger.host + ':' + TRIGGER_PORT)
		.post(this.ctx.trigger.path)
		.set('tenant-code', this.tenant)
		.send({
			name: 'Test: event #' + timestamp,
			type: 'event',
			events: param.events || [],
			callback: this.ctx.callback,
			start: param.start || new Date(),
			end: param.end || undefined,
			persistAfterEnd: param.persistAfterEnd || undefined,
			repeat: param.repeat || undefined,
			repeatInterval: param.repeatInterval || undefined,
		})
		.then(res => {
			return { tenant: this.tenant, triggerId: res.body.id, timestamp: timestamp };
		});
}

function scheduleTimer(param) {
	const when = param.when || '1 second',
		payload = {
			tenant: this.tenant,
			triggerId: param.triggerId,
			timestamp: param.timestamp,
			scheduled: helper.parseTime(when).getTime(),
		};

	return chai.request('http://' + this.ctx.trigger.host + ':' + TRIGGER_PORT)
		.post(this.ctx.trigger.path + '/' + param.triggerId + '/schedule')
		.set('tenant-code', param.tenant)
		.send({
			when: when,
			data: payload,
		}).then(res => {
			this.server.model.create({
				type: 'schedule',
				tenant: this.tenant,
				triggerId: param.triggerId,
				when: when,
				payload: payload,
			});
			payload.when = when;
			return payload;
		});
}

function listenEvent(param) {
	const events = param.events || ['a.b.c'],
		payload = {
			tenant: this.tenant,
			events: events,
			triggerId: param.triggerId,
			timestamp: param.timestamp,
			scheduled: new Date().getTime(),
		};

	return chai.request('http://' + this.ctx.trigger.host + ':' + TRIGGER_PORT)
		.post(this.ctx.trigger.path + '/' + param.triggerId + '/listen')
		.set('tenant-code', param.tenant)
		.send({
			events: events,
			data: payload,
		}).then(res => {
			return payload;
		});
}

exports.createTimer = createTimer;
exports.createEvent = createEvent;
exports.scheduleTimer = scheduleTimer;
exports.listenEvent = listenEvent;
