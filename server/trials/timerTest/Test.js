
const
	Events = require('events'),
	http = require('http'),
	_ = require('lodash'),
	mongoose = require('mongoose');

const
	Schema = mongoose.Schema;

mongoose.Promise = global.Promise;

const
	CONTEXT = {
		tenant: 'dev_young',
		db: {
			host: 'localhost',
			name: '__tests',
		},
		schema: { name: String },
		server: {
			host: 'localhost',
			port: 3000,
		},
	};

class Test extends Events {
	constructor(ctx) {
		super();

		this.ctx = _.merge(CONTEXT, ctx);
		this.tenant = this.ctx.tenant;

		// create http server
		this.server = http.createServer(this.callback.bind(this));
		this.server.listen(this.ctx.server.port);

		// connect to test results database
		mongoose.createConnection('mongodb://' + ctx.db.host + '/' + this.tenant, {
			useMongoClient: true,
		}).then(db => {
			this.db = db;
			// clear past test results
			db.dropCollection(ctx.db.name, () => {});

			this.schema = new Schema(ctx.schema);
			this.model = db.model(ctx.db.name, this.schema);
			this.emit('ready');
		});
	}

	callback(req, res) {
		this.getResponse(req).then(data => {
			this.emit('callback', data);
			res.writeHead(200, { 'Content-Type': 'text/plain' });
			res.end();
		});
	}

	getResponse(res) {
		const self = this,
			{ statusCode } = res,
			contentType = res.headers['content-type'],
			tenant = res.headers['tenant-code'];

		return new Promise((resolve, reject) => {
			res.setEncoding('utf8');
			let rawData = '';
			res.on('data', (chunk) => { rawData += chunk; });
			res.on('end', () => {
				try {
					const resp = {
						tenant: tenant,
						payload: (contentType === 'application/json') ? JSON.parse(rawData) : rawData,
					};
					resolve(resp);
				} catch (e) {
					reject(e.message);
				}
			});
		});
	}
}

module.exports = Test;
