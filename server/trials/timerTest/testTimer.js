
const
	Promise = require('bluebird'),
	_ = require('lodash'),
	debug = require('debug')('test'),
	Test = require('./Test'),
	Trigger = require('./triggerLib'),
	bm = require('../../lib/common/benchmark'),
	argv = require('minimist')(process.argv.slice(2));

const
	CONTEXT = {
		tenant: 'dev_young',
		db: {
			host: 'localhost',
			name: '__tests',
		},
		schema: {
			type: String,
			tenant: String,
			triggerId: String,
			when: String,
			error: String,
			lag: Number,
			payload: {
				tenant: String,
				timestamp: Number,
				scheduled: Number,
			},
		},
		server: {
			host: 'localhost',
			port: 3000,
		},
		trigger: {
			host: 'localhost',
			path: '/api/Triggers',
		},
	},
	SETTINGS = {
		tenant: 'dev_young',
		port: 3000,
		// start: Date.now(),
		// end: Date.now(),
		// repeat: 1,
		// repeatInterval: 0,
		// persistAfterEnd: false,
		when: '5 seconds',
		concurrency: 20,
	};

class Timer {
	constructor(param, server) {
		this.ctx = param.ctx;
		this.settings = param.settings;
		this.tenant = this.ctx.tenant;
		this.server = server;
	}

	generate() {
		return this.createTimer(this.settings).then(result => {
			result.when = this.settings.when;
			return this.scheduleTimer(result);
		});
	}
}

Timer.prototype.createTimer = Trigger.createTimer;
Timer.prototype.scheduleTimer = Trigger.scheduleTimer;

let count = 0;

run(argv);

function run(argv) {
	const arg = _.merge(SETTINGS, getParams(argv)),
		server = { host: arg.server, port: arg.port },
		ctx = _.merge(CONTEXT, { server: server }, { tenant: arg.tenant }, { trigger: { host: arg.trigger }}),
		times = arg.n || 1;		// default to 1 time

	ctx.callback =  {
		method: 'post',
		url: 'http://' + ctx.server.host + ':' + ctx.server.port + '/',
	};

	// tenant, server, port, trigger moved into context, remove from settings
	delete arg.tenant;
	delete arg.server;
	delete arg.port;
	delete arg.trigger;
	delete arg.n;

	console.log('\n---- using Trigger service: %s', ctx.trigger.host);
	console.log('[%s]  %s timers   (callback at %s:%s):', ctx.tenant, times, ctx.server.host, ctx.server.port);
	showSettings(arg);

	startTest(ctx, arg, times);
}

function getParams(argv) {
	return _.pick(argv, ['tenant', 'server', 'port', 'trigger', 'n', 'start', 'end', 'repeat', 'repeatInterval', 'persistAfterEnd', 'when', 'concurrency']);
}

function startTest(ctx, settings, times) {
	const server = new Test(ctx);

	server.on('ready', () => {
		const triggers = [];
		for (let i = 0; i < times; i++) {
			triggers.push({ param: { ctx: ctx, settings: settings }, server: server});
		}
		Promise.map(triggers, (t) => {
			return new Timer(t.param, t.server).generate();
		}, { concurrency: settings.concurrency })
			.then(() => {
				console.log('---- setup completed, waiting for triggers (do not quit until triggers complete)\n');
			}).catch(err => {
				console.log('Error:', err);
			});

		// triggers.reduce((p, f) => p.then(f), Promise.resolve()).then(() => {
		// 	console.log('---- setup completed, waiting for triggers (do not quit until triggers complete)\n');
		// }).catch(err => {
		// 	console.log('Error:', err);
		// });
	});

	server.on('callback', function(evt) {
		const lagTime = Date.now() - evt.payload.scheduled;
		server.model.create({
			type: 'callback',
			tenant: this.tenant,
			triggerId: evt.payload.triggerId,
			error: (this.tenant !== evt.payload.tenant) ? 'Mismatch tenant' : undefined,
			lag: lagTime,
			payload: evt.payload,
		});
		debug('%s   [%s] trigger: %s\tlag: %sms\t Count: %s', bm.time(), this.tenant, evt.payload.triggerId, lagTime, ++count);
	});
}

function showSettings(settings) {
	Object.keys(settings).forEach(p => {
		console.log('  %s:  %s', p, settings[p]);
	});
	console.log('');
}

module.exports = run;
