
var Trigger = require('../lib/clients/triggerClient');


var trigger = new Trigger({
	name: "CNY " + Date(),
	webhook: {
		method: "POST",
		url: "http://api.perkd.me/",
		payload: { dummy: 'triggerCLient 8888' }
	},
	persistAfterEnd: false,
	events: [ 'evt1', 'evt2' ],
	repeatInterval: 8000,
	repeat: 3
});


//---  Model inspection
console.log('\n--- [%s] Model Schema ---', trigger._settings.name);
for (var prop in trigger._model) {
	console.log('  %s: %s', prop, trigger._model[prop].type ? trigger._model[prop].type : typeof trigger._model[prop]);
}

console.log('\n--- Property Names ---');
console.log('[ %s ]', Object.keys(trigger));


//---  Properties access
console.log('\n--- Property access ---');
console.log('Values: %s\n', JSON.stringify(trigger));

trigger.name = 'Christmas 2016';
console.log('New name: %s', trigger.name);


//---  Events
trigger.on('create', function(e){
	console.log('> Event: [create] %s', JSON.stringify(e));
});

trigger.on('delete', function(e){
	console.log('> Event: [delete] %s', JSON.stringify(e));
});

trigger.on('schedule', function(e){
	console.log('> Event: [schedule] %s', JSON.stringify(e));
});


//---  Model methods

// console.log('\n--- Create & Schedule (using promise) ---')
// trigger.create()
// 	.then(function(data) {
// 		trigger.schedule('in 15 seconds');
// 		// trigger.schedule('in 15 seconds', { source: 'triggerCLient 1234' });
// 	})
// 	.fail(function(err) {
// 		console.log('Error: %s', JSON.stringify(err));
// 	});


// console.log('\n--- Create instance (using callback) ---')
// trigger.create(function(err, data){
// 	if (err) {
// 		console.log('Create error: %s', err.message);
// 	} else {
// 		console.log('Created: %s', JSON.stringify(data));

// 		// console.log('\n--- Delete instance (using callback) ---')
// 		// trigger.delete(function(err, data){
// 		// 	if (err) {
// 		// 		console.log('delete error: %s', JSON.stringify(err));
// 		// 	}
// 		// });
// 	}
// });


//---  Inititialize .json Settings file from Service  (NOT READY)
// Trigger.init('http://127.0.0.1:8012/api/Triggers', function(err, schema){
// 	if (err) {
// 		console.log('Init error: %s', JSON.stringify(err));
// 	// } else {
// 	// 	console.log('Schema: %s', JSON.stringify(schema));
// 	}
// });



//---  Illegal usage  (will throw exceptions)
// var Client = require('./lib/common/Client');
// console.log('\n--- Illegal usage ---');
// var client = new Client({ name: 'will break'});

// //  read-only properties
// delete trigger._config;						// cannot be deleted
// console.log('trigger._config: %s', JSON.stringify(trigger._config));

// trigger.className = 'xxxxxx';
// console.log('className: %s', trigger.className);		// cannot be changed

