

var data = [1, 2, 3, 4, 5, 6, 7];

console.log(data);
console.log('total(data): \t\t', total(data));
console.log('total(data, 4): \t', total(data, 4));
console.log('total(data, 8): \t', total(data, 8));
console.log('total(data, -3): \t', total(data, -3));
console.log('total(data, -8): \t', total(data, -8));

console.log('\naverage(data): \t\t', average(data));
console.log('average(data, 4): \t', average(data, 4));
console.log('average(data, 8): \t', average(data, 8));
console.log('average(data, -3): \t', average(data, -3));
console.log('average(data, -8): \t', average(data, -8));



/**
 * Aggregate total of an array
 * @param  {Array} timeSeries 	array of values
 * @param  {Number} limit  		(optional) number of values from start of array, negative limit => from end of array backwards
 * @return {Number}      		total of array
 */
function total(timeSeries, limit) {
	var total = 0;
	limit = limit || timeSeries.length;
	if (limit >= 0) {
		var start = 0;
		var end = (limit <= timeSeries.length) ? limit : timeSeries.length;
	} else {
		var start = (Math.abs(limit) <= timeSeries.length) ? (timeSeries.length + limit) : 0;
		var end = timeSeries.length;
	}
	for (var k = start; k < end; k++) {
		total += timeSeries[k];
	}
	return total;
}

function average(timeSeries, limit) {
	limit = limit || timeSeries.length;
	var count = Math.abs(limit) > timeSeries.length ? timeSeries.length : Math.abs(limit);
	return total(timeSeries, limit) / Math.abs(count);
}
