
var humanDate = require('date.js');

var testStr = [
	'10 seconds',
	'in 1 minute',
	'1 hour',
	'3 days 4 hours 20 seconds',
	'some rubbish',
	'12/11/2016',
	'now',
	'2016-08-05T04:09:05.722Z'
];

testStr.push(new Date().toISOString());
testStr.push(new Date());

// console.log(testStr);


testStr.forEach(function(time) {
	console.log('%s: \t%s', time, parseTime(time));
});

function parseTime(time) {
	if (time instanceof Date) return time;
	var isDate = Date.parse(time);
	if (isNaN(isDate)) return humanDate(time);
	else return new Date(isDate);
}

// console.log(new Date('rubbish') == 'Invalid Date');
// console.log(Date.parse('rubbish'));