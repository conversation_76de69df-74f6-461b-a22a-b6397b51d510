{"name": "agenda-test", "version": "1.0.0", "description": "", "main": "server.js", "dependencies": {"agenda": "^0.8.1", "agendash": "^0.3.2", "mongodb": "^2.2.5"}, "devDependencies": {}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js"}, "repository": {"type": "git", "url": "git+https://github.com/rschmukler/agenda.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/rschmukler/agenda/issues"}, "homepage": "https://github.com/rschmukler/agenda#readme"}