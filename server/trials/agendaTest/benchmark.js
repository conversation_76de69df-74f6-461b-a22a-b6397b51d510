
var _log = {},		// stores history of all markers, identical named markers will override previous value
	_max = {},
	_min = {},
	_active = true;

exports.mark = function(marker) {
	if (_active) {
		var ts =  process.hrtime();		// current high-resolution real time in a [seconds, nanoseconds]
		if (marker)  _log[marker] = ts;
		return ts;
	}
};

exports.get = function(marker) {
	return _log[marker];
};

exports.elapsedTime = function(start, end, verbose) {
	if (_active) {
		if (typeof end == 'boolean' || (end == undefined && verbose == undefined)) {
			verbose = (typeof end == 'boolean') ? end : verbose;
			end = start + '.end';
			start += '.start';
			var shorthand = true;
		}

		if (_log[start] && _log[end]) {
			var elapsed = exports.diff(_log[start], _log[end]);

			_max[start] = _max[start] ? Math.max(_max[start], elapsed) : elapsed;
			_min[start] = _min[start] ? Math.min(_min[start], elapsed) : elapsed;

			if (verbose) {
				console.log('>>> Benchmark: ' + start + '->' + end + ':  \t' + elapsed + ' ms');
			}
			return elapsed;
		} else {
			return -1;
		}
	}
};

exports.diff = function(start, end) {
	return Math.round((((end[0] - start[0]) * 1e9 + (end[1] - start[1])) / 1000000) * 1000) / 1000;		// return in milliseconds
};

exports.time = function(date) {
	date = date || new Date();
	return date.toTimeString().substring(0, 8);
};

exports.now = function() {
	return Date.now();
};

exports.max = function(marker) {
	return _max[marker];
};

exports.min = function(marker) {
	return _min[marker];
};

exports.on = function() {
	_active = true;
};

exports.off = function() {
	_active = false;
};

exports.reset = function() {
	_log = {};
	_max = {};
	_min = {};
};
