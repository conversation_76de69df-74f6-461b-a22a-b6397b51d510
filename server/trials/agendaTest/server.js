
var Agenda = require('agenda'),
	Agendash = require('agendash'),
	express = require('express'),
	async = require('async'),
	bm = require('./benchmark');

var mongoConnectionString = "mongodb://127.0.0.1/agenda";

var options = {
	name: 'Timer',
	db: {
		address: mongoConnectionString,
		// options: {
		// 	server: { auto_reconnect:true }
		// }
	},
	processEvery: 5000,
	maxConcurrency: 2000,
	defaultConcurrency: 20,
	// lockLimit: 0,
	// defaultLockLimit: 0,
	defaultLockLifetime: 60000
};

var app = express();
var agenda = new Agenda(options);

// or override the default collection name:
// var agenda = new Agenda({db: {address: mongoConnectionString, collection: "jobCollectionName"}});

// or pass additional connection options:
// var agenda = new Agenda({db: {address: mongoConnectionString, collection: "jobCollectionName", options: {server:{auto_reconnect:true}}}});

// or pass in an existing mongodb-native MongoClient instance
// var agenda = new Agenda({mongo: myMongoClient});


function oneJob() {
	agenda.define('one job', async (job) => {
		console.log('%s  Job: ', bm.time(), job);
	});
	agenda.schedule('5 seconds', 'one job');
}


function simpleTest() {
	agenda.define('every 10 seconds', async (job) => {
		console.log('%s  >> every 10 seconds', bm.time());

		// setTimeout(function() {
		// 	console.log('%s  << every 10 seconds', bm.time());
		// }, 5000);
	});

	agenda.define('schedule 20 seconds later', async (job) => {
		console.log('%s  --- scheduled 20 seconds later', bm.time());
	});

	// agenda.define('every 1 second', function(job, done) {
	// 	console.log('%s', bm.time());
	// 	done();
	// });

	agenda.every('10 seconds', 'every 10 seconds').then(job => {
		console.log('job: [every 10 seconds] started');
	});

	agenda.schedule('20 seconds', 'schedule 20 seconds later').then(job => {
		console.log('job: [schedule 20 seconds later] started');
	});

	// agenda.every('1 second', 'every 1 second');
}


function query(callback) {
	agenda._collection.aggregate([
      {$project: {
        _id: 0,
        job: '$$ROOT',
        nextRunAt: {$ifNull: ['$nextRunAt', 0]},
        lockedAt: {$ifNull: ['$lockedAt', 0]},
        lastRunAt: {$ifNull: ['$lastRunAt', 0]},
        lastFinishedAt: {$ifNull: ['$lastFinishedAt', 0]},
        failedAt: {$ifNull: ['$failedAt', 0]},
        repeatInterval: {$ifNull: ['$repeatInterval', 0]}
      }},
      {$project: {
        _id: 0,
        job: '$$ROOT',
        nextRunAt: {$ifNull: ['$nextRunAt', 0]},
        lockedAt: {$ifNull: ['$lockedAt', 0]},
        lastRunAt: {$ifNull: ['$lastRunAt', 0]},
        lastFinishedAt: {$ifNull: ['$lastFinishedAt', 0]},
        failedAt: {$ifNull: ['$failedAt', 0]},
        repeatInterval: {$ifNull: ['$repeatInterval', 0]}
      }},
      {$project: {
        job: '$job',
        _id: '$job._id',
        running: {$and: [
          '$lastRunAt',
          {$gt: [ '$lastRunAt', '$lastFinishedAt' ]}
        ]},
        scheduled: {$and: [
          '$nextRunAt',
          {$gte: [ '$nextRunAt', new Date() ]}
        ]},
        queued: {$and: [
          '$nextRunAt',
          {$gte: [ new Date(), '$nextRunAt' ]},
          {$gte: [ '$nextRunAt', '$lastFinishedAt' ]}
        ]},
        completed: {$and: [
          '$lastFinishedAt',
          {$gt: ['$lastFinishedAt', '$failedAt']}
        ]},
        failed: {$and: [
          '$lastFinishedAt',
          '$failedAt',
          {$eq: ['$lastFinishedAt', '$failedAt']}
        ]},
        repeating: {$and: [
          '$repeatInterval',
          {$ne: ['$repeatInterval', null]}
        ]}
      }},
    ]).toArray(function (err, results) {
      if (err) return callback(err)
      callback(null, results)
    })
}



var completed = 0,
	scheduled = 0,
	count = 0,
	exceeded = 0,
	TOLERANCE = 500;	// milliseconds

function stress(total, schedule, interval) {
	var tasks = [];
	var lastRun = null;

	console.log('Repeat interval: %s ms (tolerance: %s ms)', interval, TOLERANCE);

	for (var i=1; i <= total; i++) {

		agenda.define('stress #'+i, function(job, done) {
			done();
			completed++;

			if (job.attrs.name == 'stress #'+total) {
				count++;
				if (!lastRun)    lastRun = bm.mark();
				else {
					var timeTaken = bm.diff(lastRun, bm.mark());
					if (timeTaken > (interval + TOLERANCE)) {
						console.log('%s  interval exceeded (%s ms): \t%s% of %s', bm.time(), timeTaken, Math.round((++exceeded / count)*1000) / 10, count);
					}
					if (count % 20 == 0)   console.log('%s  completed %s  (exceeded: %s%)', bm.time(), completed, Math.round((exceeded / count)*1000) / 10);
					lastRun = bm.mark();
				}
			}
		});
		tasks.push(schedule);
	}

	async.parallel(tasks, function(err, result) {
		if (err)  console.log('error: ', err);
		console.log('completed scheduling %s timers\n', scheduled);
	});
}


agenda.on('ready', function() {

	// agenda.purge(function(err, numRemoved) {
	// 	console.log('%s jobs removed\n', numRemoved);

		console.log('_definitions', agenda._definitions);
		
		agenda.start();
		console.log('%s   ----- start -----', bm.time());
		
		
		// query(function(err, jobs) {
			// 	console.log(jobs);
			// 	console.log(jobs[0].job.job);
			// });
			
			
		//--- different test cases ----
		// oneJob();
		simpleTest();

		// test 3
		// agenda.schedule('8 second', 'stress #' + (++scheduled), function (err, res) { done(null, scheduled); });

		// stress(1000, recur, 1000);
		// console.log('_definitions', agenda._definitions);

	// });

	app.use('/agendash', Agendash(agenda));
	app.listen(3000);

	var once = function (done) {
		agenda.schedule('1 second', 'stress #'+(++scheduled), function(err, res) { done(null, scheduled); });
	};

	var recur = function (done) {
		agenda.every('1 second', 'stress #'+(++scheduled), function(err, res) { done(null, scheduled); });
	};

});

process.on('SIGTERM', function() {
	terminated();
});

process.on('SIGINT', function() {
	terminated();
});

function terminated() {
	agenda.stop();
	console.log('\nTerminated\n');
	process.exit(0);
}
