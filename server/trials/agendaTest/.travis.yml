language: node_js
services: mongodb
sudo: required
node_js:
  - "0.11"
  - "0.10"
  - "4"
  - "5"
  - "6"
script: make test-once
before_script:
  - sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv 7F0CEB10
  - echo 'deb http://downloads-distro.mongodb.org/repo/ubuntu-upstart dist 10gen' | sudo tee /etc/apt/sources.list.d/mongodb.list
  - sudo apt-get update
  - sudo apt-get install -y mongodb-org=2.6.6 mongodb-org-server=2.6.6 mongodb-org-shell=2.6.6 mongodb-org-mongos=2.6.6 mongodb-org-tools=2.6.6
  - sleep 15 #mongo may not be responded directly. See http://docs.travis-ci.com/user/database-setup/#MongoDB
  - mongo --version
after_script: make test-coveralls
before_install:
  # <PERSON> uses an ancient GCC
  - export CC="gcc-4.9" CXX="g++-4.9"
  # node 0.8 comes with a non-functional version of npm
  - "if [[ $(node --version) == v0.8.* ]]; then npm install -g npm@2.1.18; fi"
