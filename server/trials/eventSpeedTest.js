
var Speed = require('../lib/eventSpeed');

var timer;
var EVENT = 'x.card.add';

var speed = new Speed();

speed.on('event.speed', function(evt) {
	console.log('[%s]: %s per minute \tper second: %s  (5sec avg: %s)', evt.name, evt.minute, evt.second, evt.avgSecond);
});


// setTimeout(function() {
	run();
	stopAfter(150000);
// }, 3000);

function run() {
	timer = setInterval(function() {
		var rand = (Math.random() * 100).toFixed();
		for (var i = 0; i < 10; i++) {
			speed.count(EVENT);
		}
	}, 100);
}

function stopAfter(duration) {
	setTimeout(function() {
		clearInterval(timer);
		console.log('------  ended');
	}, duration);
}

