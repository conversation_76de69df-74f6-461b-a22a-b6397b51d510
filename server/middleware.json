{"initial": {"compression": {}, "cors": {"params": {"origin": true, "credentials": true, "maxAge": 86400}}, "loopback#favicon": {}}, "session": {}, "auth": {"./lib/common/middleware/multitenant": {"name": "multitenant", "paths": ["/api/Triggers"], "params": {"tenant-code": "trap"}, "enabled": true}}, "parse:before": {"./lib/common/middleware/multitenant-ds": {"name": "multitenant-ds", "paths": ["/api/Triggers"], "enabled": true}}, "parse": {}, "routes": {"loopback#rest": {"paths": ["${restApiRoot}"]}}, "files": {}, "final": {"loopback#urlNotFound": {}}, "final:after": {"./lib/common/middleware/error-handler": {"name": "error-handler", "paths": ["${restApiRoot}"], "enabled": true}, "strong-error-handler": {"params": {"debug": true, "log": true, "safeFields": ["code", "details"]}}}}