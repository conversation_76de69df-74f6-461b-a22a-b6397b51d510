/**
 * @module Mixin:Request
 */

const { Triggers } = require('@crm/types'),
	{ bm } = require('@perkd/utils')

const { SCHEDULED } = Triggers.State,
	{ REQUEST } = Triggers.Type

module.exports = function(Trigger) {

	/**
	 * Setup a REQUEST trigger
	 * @return {Trigger} updated instance
	 */
	Trigger.prototype.handle = async function() {
		const { id, start, end, repeat, callback } = this,
			{ app } = Trigger,
			{ Event, Metric } = app,
			changes = {
				type: REQUEST,
				state: SCHEDULED,
				active: true,
				start: start || new Date(),
				end,
				callback,
				nextRunAt: undefined,
				repeatRemaining: repeat > 0 ? repeat : undefined, // reset countdown
			}

		try {
			await this.updateAttributes(changes)
			app.emit(Event.trigger.SCHEDULED, { id, type: this.type })
		}
		catch (err) {
			appMetric(Metric.schedule.error, 1, { child: 'request' })
		}

		return this
	}

	/**
	 * Endpoint to accept Requests from Event Gateway
	 * @param {Object} data - request data/parameters
	 * @return {Object} callback result
	 */
	Trigger.prototype.request = async function(data) {
		const { id } = this,
			{ app } = Trigger,
			{ Event, Metric } = app,
			options = { data, param: data },
			bmStart = bm.mark()

		appMetric(Metric.execute.count, 1, { child: 'request' })

		try {
			const result = await Trigger._doCallback(this, options)

			app.emit(Event.trigger.REQUEST, { id, result })
			appMetric(Metric.execute.latency, bm.diff(bmStart), { child: 'request' })

			return result
		}
		catch (error) {
			app.emit(Event.trigger.REQUEST, { id, error })
			appMetric(Metric.execute.error, 1, { child: 'request' })
		}
	}
}
