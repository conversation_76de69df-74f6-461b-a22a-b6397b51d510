/**
 * @module Mixin:Job - Timer/Agenda-specific methods
 */
const { Context } = require('@perkd/multitenant-context')

module.exports = function(Trigger) {

	Trigger.jobsScheduled = async function(tenant) {
		const scheduled = {
			'data.tenantCode': tenant,
			nextRunAt: { $gte: new Date() },
		}

		return Trigger.app.Service.main.timer.jobs(scheduled)
	}

	Trigger.jobsQueued = async function(tenant) {
		const queued = { $and: [
			{ 'data.tenantCode': tenant },
			{ nextRunAt: { $lt: new Date() } },
			{ $or: [
				{ lastFinishedAt: { $exists: false } },
				{ $where: 'this.lastFinishedAt < this.nextRunAt' },
			] },
		] }

		return Trigger.app.Service.main.timer.jobs(queued)
	}

	// Trigger.jobsCompleted = function() {
	// 	const completed = { $and: [
	// 		{ lastFinishedAt: { $exists: true } },
	// 		{ $or: [
	// 			{ failedAt: { $exists: false } },
	// 			{ $where: 'this.failedAt < this.lastFinishedAt' },
	// 		] },
	// 	] };

	// 	return Trigger.app.Service.main.timer.jobs(completed);
	// };

	// Trigger.jobsFailed = function() {
	// 	const failed = { $and: [
	// 		{ lastFinishedAt: { $exists: true } },
	// 		{ failedAt: { $exists: true } },
	// 		{ $where: 'this.lastFinishedAt = this.failedAt' },
	// 	] };

	// 	return Trigger.app.Service.main.timer.jobs(failed);
	// };

	Trigger.removeJob = async function(job) {
		const { Service } = Trigger.app,
			Timer = Service.main.timer

		await job.remove()
		delete Timer._definitions[job.attrs.name]
	}

	// -----  Instance Methods  -----

	Trigger.prototype.job = async function() {
		const { id, jobId } = this,
			{ Service } = Trigger.app,
			Timer = Service.main.timer,
			filter = {
				'data.tenantCode': Context.tenant,
				'data.triggerId': id.toString(),
			},
			[ job ] = await Timer.jobs(filter)

		if (!job) throw new Error(`Agenda job missing (id: ${jobId})`)

		return job
	}
}
