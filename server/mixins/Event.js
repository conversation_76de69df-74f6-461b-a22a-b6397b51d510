/**
 * @module Mixin:Event
 */

const { Triggers } = require('@crm/types'),
	{ Context } = require('@perkd/multitenant-context'),
	{ bm } = require('@perkd/utils')

const { SCHEDULED } = Triggers.State,
	{ EVENT } = Triggers.Type

module.exports = function(Trigger) {

	Trigger.restoreEventHandlers = async function(tenant) {
		const { eventbus } = Trigger.app.Service,
			activeEvents = {
				where: { and: [
					{ type: EVENT },
					{ or: [
						{ end: null },
						{ end: { exists: false } },
						{ end: { gte: new Date() } },
					] },
				] } }

		try {
			const triggers = await Trigger.find(activeEvents),
				receipts = []

			for (const { name, events } of triggers) {
				for (const event of events) {
					await eventbus.subscribe(event, tenant, Trigger.eventHandler)
				}
				receipts.push({ name, events })
			}

			return receipts.length ? receipts : undefined
		}
		catch (err) {
			console.error('[restoreEventHandlers]', err)
		}
	}

	/**
	 * Handle Events received from EventBus
	 *
	 * @param  {Object} event event object { name: 'eventName', data: { event payload } }
	 * @return  {Object[]} list of callback response
	 */
	Trigger.eventHandler = async function(event) {
		const { app } = Trigger,
			{ Event, Metric } = app,
			{ eventbus } = appModule(),
			{ tenant } = Context,
			{ name, data } = event,
			options = { data, param: event },
			eventTriggers = {
				where: {
					and: [
						{ type: EVENT },
						{ events: name },
					// { active: true },
					// { start: { lte: now } },
					// { or: [ { end: null }, { end: { gte: now }} ] }
					],
				},
			},
			bmStart = bm.mark()

		appMetric(Metric.execute.count, 1, { child: 'event' })

		const triggers = await Trigger.find(eventTriggers),
			callbacks = []

		if (!triggers.length) {
			//  No active triggers found, do some housekeeping...
			await eventbus.unsubscribe(event.name, tenant)		// 1. unsubscribe from this event
			Trigger.houseKeep()
			return
		}

		for (const trigger of triggers) {
			callbacks.push(
				Trigger._doCallback(trigger, options).catch(() => null).then(() => {
					app.emit(Event.trigger.EVENT, { id: trigger.id, event })
				})
			)
		}

		Promise.all(callbacks)
			.catch(() => {
				appMetric(Metric.execute.error, 1, { child: 'event' })
			})
			.then(() => {
				appMetric(Metric.execute.latency, bm.diff(bmStart), { child: 'event' })
			})
	}

	/**
	 * Setup an EVENT trigger
	 * @param {Array}   events  list of event name(s) to listen to
	 * @return {Trigger} updated instance or on reject: { events, error }
	 */
	Trigger.prototype.listen = async function(events) {
		const { id, start, end, repeat, callback } = this,
			{ app } = Trigger,
			{ Event, Metric, Service } = app,
			{ eventbus } = Service,
			{ tenant } = Context

		if (events && !Array.isArray(events)) events = [ events ]
		this.events = events || this.events || []

		// --- validations ---					// TODO need more stringent checks ??
		if (!this.events.length) {
			throw new Error('No events to listen')
		}

		try {
			const changes = {
				type: EVENT,
				state: SCHEDULED,
				events: this.events,
				active: true,
				start: start || new Date(),
				end,
				callback,
				nextRunAt: undefined,
				repeatRemaining: repeat > 0 ? repeat : undefined, // reset countdown
			}

			await this.updateAttributes(changes)

			for (const event of this.events) {
				await eventbus.subscribe(event, tenant, Trigger.eventHandler)
			}

			app.emit(Event.trigger.SCHEDULED, { id, type: this.type, events: this.events })

			return this
		}
		catch (err) {
			console.error('[listen]', err)
			appMetric(Metric.schedule.error, 1, { child: 'event' })
			return this
		}
	}
}
