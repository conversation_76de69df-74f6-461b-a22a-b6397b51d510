/**
 * @module Mixin:Timer
 */
const { Triggers, Settings } = require('@crm/types'),
	{ Context } = require('@perkd/multitenant-context'),
	{ parseWhen } = require('@perkd/timers'),
	{ bm } = require('@perkd/utils')

const { SCHEDULED } = Triggers.State,
	{ TIMER } = Triggers.Type

module.exports = function(Trigger) {

	Trigger.defineTimer = function(tenantCode, id, handler) {
		const { app } = Trigger,
			{ main } = appModule()

		main.timer.define(tenantCode, String(id), async (data, tenant) => {
			// Create a single connection context
			await Context.runAsTenant(tenant, async context => {
				// Pass the context to the handler
				await handler(data, context)
			}, app.connectionManager)
		})
	}

	/**
	 * Setup a TIMER trigger
	 *
	 * @param {String} when - time to start trigger, accepts Date obj, date string OR human time: http://date.js.org
	 * @return {Promise}
	 */
	Trigger.prototype.schedule = async function(when) {
		const { id, name, type, start, end, repeat, repeatInterval, timezone } = this,
			{ app } = Trigger,
			{ Event, Metric, Service } = app,
			{ timer: Timer } = Service.main,
			triggerId = String(id),
			{ tenant } = Context,
			{ timeZone } = app.getSettings(Settings.LOCALE),
			startTime = (when ? parseWhen(when, timezone || timeZone) : start) || new Date(),	// will default to now if 'when' is invalid
			options = { repeat, repeatInterval, end, timezone: timezone || timeZone },
			data = { triggerId }		// FIXME

		Trigger.defineTimer(tenant, triggerId, handler)

		try {
			const { delay } = await Timer.schedule(triggerId, startTime, data, options),
				nextRunAt = new Date(Date.now() + delay),
				changes = {
					type: TIMER,
					state: SCHEDULED,
					active: true,
					start: startTime,
					end,
					nextRunAt,
					repeatRemaining: (repeatInterval && repeat) ? repeat : undefined,	// setup for countdown
				}

			await this.updateAttributes(changes)
			app.emit(Event.trigger.SCHEDULED, { id, type, nextRunAt })
		}
		catch (error) {
			appMetric(Metric.schedule.error, 1, { child: 'timer' })
			app.emit('watchdog.error', [ 'schedule.timer', { startTime, name, data, error } ])
		}

		return this
	}

	/**
	 * @param	{String} tenant
	 * @return	{Number} number of timers canceled
	 */
	Trigger.restoreTimerHandlers = async function(tenant) {
		const { timer: Timer } = Trigger.app.Service.main,
			scheduled = await Timer.schedules.list(tenant),
			receipt = []

		for (const id of scheduled) {
			Trigger.defineTimer(tenant, id, handler)
			receipt.push({ id })
		}

		return receipt.length ? receipt : undefined
	}

	Trigger.prototype.cancelTimers = async function() {
		const { id } = this,
			{ timer: Timer } = Trigger.app.Service.main

		await Timer.cancel(String(id))
			.catch(err => {
				console.error('Error canceling timer', err)
			})
	}

	// ---  Private Methods  ---

	/**
	 * Handler called by Timer
	 * @param {Object} data
	 * @return {Trigger} instance
	 */
	async function handler(data = {}, context) {
		const { triggerId = null } = data,
			{ app } = Trigger,
			{ Event, Metric } = app,
			bmStart = bm.mark()

		try {
			const trigger = await Trigger.findById(triggerId, { context }),
				{ callback = {} } = trigger,
				{ payload: param } = callback,
				options = { param }

			app.emit(Event.trigger.TIMER, { id: triggerId })
			appMetric(Metric.execute.count, 1, { child: 'timer' })

			await Trigger._doCallback(trigger, options).catch(err => null)		// suppress errors

			appMetric(Metric.execute.latency, bm.diff(bmStart), { child: 'timer' })
		}
		catch (error) {
			console.error('[handler]', { triggerId, error })
		}
	}
}
