diff --git a/lib/mongo_client.js b/lib/mongo_client.js
index e9cbbe78ee8a1da9cc12d0d1322a8809eed0403d..57abb8778712bb0f36b7a0a8ee90d2c2ae94cde0 100644
--- a/lib/mongo_client.js
+++ b/lib/mongo_client.js
@@ -15,6 +15,12 @@ const NativeTopology = require('./topologies/native_topology');
 const connect = require('./operations/connect').connect;
 const validOptions = require('./operations/connect').validOptions;
 
+/**
+ * connectionCount: in-mem counter
+ */
+let connectionCount = 0,
+  disconnectionCount = 0,
+  totalConnection = 0
 /**
  * @fileOverview The **MongoClient** class is a class that allows for making Connections to MongoDB.
  *
@@ -272,6 +278,13 @@ Object.defineProperty(MongoClient.prototype, 'readPreference', {
  * @return {Promise<MongoClient>} returns Promise if no callback passed
  */
 MongoClient.prototype.connect = function(callback) {
+  /**
+   * connectionCount Logger: Logs the number of mongodb connection request
+   */
+  console.log('MONGODB_CONNECTION_REQUEST: ', ++connectionCount)
+  console.log('MONGODB_TOTAL_CONNECTION_COUNT_CALL: ', ++totalConnection)
+  global.appStats('database.conn.request')
+  global.appStats('database.conn.total', totalConnection)
   if (typeof callback === 'string') {
     throw new TypeError('`connect` only accepts a callback');
   }
@@ -301,6 +314,10 @@ MongoClient.prototype.logout = deprecate(function(options, callback) {
  * @return {Promise} returns Promise if no callback passed
  */
 MongoClient.prototype.close = function(force, callback) {
+  console.log('MONGODB_DISCONNECTION_REQUEST: ', ++disconnectionCount)
+  console.log('MONGODB_TOTAL_CONNECTION_COUNT_CALL: ', --totalConnection)
+  global.appStats('database.disconn')
+  global.appStats('database.conn.total', totalConnection)
   if (typeof force === 'function') {
     callback = force;
     force = false;
