//  Module Dependencies
const Promise = require('bluebird'),
	assert = require('assert'),
	moment = require('moment'),
	app = require('../server/server'),
	TRIGGERCLASS = require('../server/lib/common/mochaTrigger');

const Trigger = app.models.Trigger,
	settings = {
		source: { host: '127.0.0.1', port: '8012', apiPath: '/api/Triggers' },
	},
	EVENTS = ['dev.trigger.test1', 'dev.trigger.test2'],
	TRIGGER = new TRIGGERCLASS(app, null, settings),
	CONCURRENCY = 10;

describe('Event trigger', () => {
	describe('Perpetual (no start/end time)', () => {
		let triggerId;
		const payload = {};

		it('should create trigger', () => {
			return TRIGGER.create().then(({ id }) => {
				triggerId = id.toString();
				return TRIGGER.listen(triggerId, EVENTS, payload);
			});
		});

		it('should fire 1 event and receive response', () => {
			return fireEventAndCheck(payload);
		});

		it('should be COMPLETED', () => {
			return Promise.delay(200).then(() => TRIGGER.stateIs(triggerId, Trigger.State.COMPLETED));
		});

		it('should fire event another time and receive callback', () => {
			return fireEventAndCheck(payload);
		});
	});

	describe('Limited times (persist)', () => {
		let triggerId;
		const REPEAT = 5,
			payload = {};

		it('should create trigger (persist)', () => {
			return TRIGGER.create({ repeat: REPEAT, persistAfterEnd: true }).then(({ id }) => {
				triggerId = id.toString();
				return TRIGGER.listen(triggerId, EVENTS, payload);
			});
		});

		it('should have ' + REPEAT + ' times remaining', () => {
			return TRIGGER.runAsTenant(() => {
				return Trigger.findById(triggerId).then(trigger => {
					assert.equal(trigger.repeat, REPEAT, 'repeat');
					assert.equal(trigger.repeatRemaining, REPEAT, 'repeatRemaining');
				});
			});
		});

		it('should fire event ' + REPEAT + ' times and receive response', () => {
			return fireEventAndCheck({}, REPEAT);
		});

		it('should have 0 times remaining', () => {
			return TRIGGER.runAsTenant(() => {
				return Trigger.findById(triggerId).then(trigger => {
					assert.equal(trigger.repeatRemaining, 0, 'repeatRemaining');
				});
			});
		});

		it('should be ENDED', () => {
			return TRIGGER.stateIs(triggerId, Trigger.State.ENDED);
		});
	}).timeout(5000);

	describe('Limited times (auto-delete)', () => {
		let triggerId;
		const REPEAT = 3000;

		it('should create trigger (default persistAfterEnd)', () => {
			return TRIGGER.create({ repeat: REPEAT }).then(({ id }) => {
				triggerId = id.toString();
				return TRIGGER.listen(triggerId, EVENTS, {});
			});
		});

		it('should have ' + REPEAT + ' times remaining', () => {
			return TRIGGER.runAsTenant(() => {
				return Trigger.findById(triggerId).then(trigger => {
					assert.equal(trigger.repeat, REPEAT, 'repeat');
					assert.equal(trigger.repeatRemaining, REPEAT, 'repeatRemaining');
				});
			});
		});

		it('should fire event ' + REPEAT + ' times and receive callback', () => {
			return fireEventAndCheck(null, REPEAT);
		});

		it('should be auto-deleted', () => {
			return TRIGGER.isAutoDeleted(triggerId);
		});
	}).timeout(600000);

	describe('Time-limited', () => {
		let triggerId;
		const START = 5,	// seconds later
			END = 15,		// seconds later
			event = {
				name: EVENTS[0],
				id: new Date().getTime(),
				key: process.hrtime()[1],
			};

		it('should create trigger', () => {
			const start = moment().add(START, 'seconds').toDate(),
				end = moment().add(END, 'seconds').toDate();

			return TRIGGER.create({ start: start, end: end }).then(({ id }) => {
				triggerId = id.toString();
				return TRIGGER.listen(triggerId, EVENTS, {});
			});
		});

		it('should FAIL when receiving event BEFORE start time', () => {
			return TRIGGER.eventHandler(event).then(res => {
				assert.equal(res, '', 'res === ""');
			});
		});

		it('should fire event & get callback after start time', () => {
			return Promise.delay(START * 1000).then(() => {
				return fireEventAndCheck();
			});
		}).timeout((START + 1) * 1000);

		it('should FAIL when receiving event AFTER end time', () => {
			return Promise.delay((END - START) * 1000).then(() => {
				return TRIGGER.eventHandler(event).then(res => {
					assert.equal(res, '', 'res === ""');
				});
			});
		}).timeout((END - START + 2) * 1000);

		it('should be auto-deleted', () => {
			return TRIGGER.isAutoDeleted(triggerId);
		});
	});

	function fireEventAndCheck(payload, times = 1) {
		const events = [];

		for (let i = 0; i < times; i++) {
			const event = {
				name: EVENTS[0],
				id: new Date().getTime(),
				key: process.hrtime()[1],
			}; // used for matching callbacks)]);
			events.push([Object.assign({}, event, payload || {})]);
		}
		return Promise.map(events, (params) => _fireOnce(...params), { concurrency: CONCURRENCY });

		function _fireOnce(event) {
			return new Promise((resolve, reject) => {
				TRIGGER.waitForCallback(null, event).then(res => {
					delete res.echo;	// added by handle()
					delete res._triggerId;
					assert.deepEqual(res, event, 'payload');
					resolve(res);
				}).catch(err => reject(err));

				TRIGGER.eventHandler(event);
			});
		}
	}

	before(() => {
		return app.init(null, true).then(() => {
			TRIGGER.startServer();
			return TRIGGER.deleteAll();
		});
	});

	after(() => TRIGGER.stopServer());
});
