
//  Module Dependencies
const app = require('../server/server'),
	moment = require('moment'),
	TRIGGERCLASS = require('../server/lib/common/mochaTrigger');

const Trigger = app.models.Trigger,
	settings = {
		source: { host: '127.0.0.1', port: 8012, apiPath: '/api/Triggers' },
	},
	TRIGGER = new TRIGGERCLASS(app, null, settings);

// ----- test data -----

describe('Timer trigger', () => {
	describe('One-time (persist)', () => {
		let triggerId;

		it('should create timer', () => {
			return TRIGGER.create({ persistAfterEnd: true }).then(({ id }) => (triggerId = id.toString()));
		});

		it('should schedule timer `30 seconds` (relative)', () => {
			return TRIGGER.schedule(triggerId, '30 seconds');
		});

		it('should receive callback', () => {
			return TRIGGER.waitForCallback();
		}).timeout(35000);

		it('should be ENDED', () => {
			return TRIGGER.stateIs(triggerId, Trigger.State.ENDED);
		});
	});

	describe('One-time (auto-delete)', () => {
		let triggerId;

		it('should create timer with payload', () => {
			const payload = { data: 'will be overwritten' };

			return TRIGGER.create({ persistAfterEnd: false, callback: { payload: payload } })
				.then(({ id }) => (triggerId = id.toString()));
		});

		it('should schedule timer (absolute, in 5 sec)', () => {
			const time = moment().add(5, 'seconds').toDate();
			return TRIGGER.schedule(triggerId, time, { data: 'new payload' });
		});

		it('should receive callback', () => {
			return TRIGGER.waitForCallback();
		}).timeout(6000);

		it('should be auto-deleted', () => {
			return TRIGGER.isAutoDeleted(triggerId);
		});
	});

	describe('Recurring', () => {
		let triggerId;
		const REPEAT = 3,
			INTERVAL = 30000;

		it('should create timer (repeat 3 times, 30 seconds apart)', () => {
			return TRIGGER.create({ repeat: REPEAT, repeatInterval: INTERVAL, persistAfterEnd: false })
				.then(({ id }) => (triggerId = id.toString()));
		});

		it('should schedule timer (in 7 sec)', () => {
			return TRIGGER.schedule(triggerId, '7 seconds', null, false);		// don't have ETA check
		});

		it('should receive ' + REPEAT + ' callbacks', () => {
			return TRIGGER.waitForCallback(7000 - 100).then(() => {
				return TRIGGER.waitForCallback(INTERVAL - 100).then(() => {
					return TRIGGER.waitForCallback(INTERVAL - 100);
				});
			});
		}).timeout(INTERVAL * REPEAT + 2000);

		it('should be auto-deleted', () => {
			return TRIGGER.isAutoDeleted(triggerId);
		});
	});

	before(() => {
		const SETTINGS = require('../server/config/settings.json');
		console.log('\tprocessEvery: %sms\n', SETTINGS.agenda.processEvery);
		return app.init(null, true).then(() => {
			TRIGGER.startServer();
			return TRIGGER.deleteAll();
		});
	});

	after(() => TRIGGER.stopServer());
});
