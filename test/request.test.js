
//  Module Dependencies
const Promise = require('bluebird'),
	assert = require('assert'),
	moment = require('moment'),
	app = require('../server/server'),
	TRIGGERCLASS = require('../server/lib/common/mochaTrigger');

const Trigger = app.models.Trigger,
	settings = {
		source: { host: '127.0.0.1', port: '8012', apiPath: '/api/Triggers' },
	},
	TRIGGER = new TRIGGERCLASS(app, null, settings),
	CONCURRENCY = 10;

// ----- test data -----

describe('Request trigger', () => {
	describe('Perpetual (no start/end time)', () => {
		let triggerId;
		const payload = { some: new Date().getTime() },
			requestParam = { parameters: 'something important' };

		it('should create trigger', () => {
			return TRIGGER.create().then(({ id }) => {
				triggerId = id.toString();
				return TRIGGER.handle(triggerId, payload);
			});
		});

		it('should request 1 time and receive callback', () => {
			return requestAndWait(triggerId, requestParam, payload);
		});

		it('should be COMPLETED', () => {
			return TRIGGER.stateIs(triggerId, Trigger.State.COMPLETED);
		});

		it('should request another time and receive callback', () => {
			return requestAndWait(triggerId, requestParam, payload);
		});
	});

	describe('Limited times (persist)', () => {
		let triggerId;
		const REPEAT = 5;

		it('should create trigger (persist)', () => {
			return TRIGGER.create({ repeat: REPEAT, persistAfterEnd: true }).then(({ id }) => {
				triggerId = id.toString();
				return TRIGGER.handle(triggerId);
			});
		});

		it('should have ' + REPEAT + ' times remaining', () => {
			return TRIGGER.runAsTenant(() => {
				return Trigger.findById(triggerId).then(trigger => {
					assert.equal(trigger.repeat, REPEAT, 'repeat');
					assert.equal(trigger.repeatRemaining, REPEAT, 'repeatRemaining');
				});
			});
		});

		it('should request ' + REPEAT + ' times and receive callback', () => {
			return requestAndWait(triggerId, null, null, REPEAT);
		});

		it('should have 0 times remaining', () => {
			return TRIGGER.runAsTenant(() => {
				return Trigger.findById(triggerId).then(trigger => {
					assert.equal(trigger.repeatRemaining, 0, 'repeatRemaining');
				});
			});
		});

		it('should be ENDED', () => {
			return TRIGGER.stateIs(triggerId, Trigger.State.ENDED);
		});
	}).timeout(5000);

	describe('Limited times (auto-delete)', () => {
		let triggerId;
		const REPEAT = 3000;

		it('should create trigger (default persistAfterEnd)', () => {
			return TRIGGER.create({ repeat: REPEAT }).then(({ id }) => {
				triggerId = id.toString();
				return TRIGGER.handle(triggerId);
			});
		});

		it('should have ' + REPEAT + ' times remaining', () => {
			return TRIGGER.runAsTenant(() => {
				return Trigger.findById(triggerId).then(trigger => {
					assert.equal(trigger.repeat, REPEAT, 'repeat');
					assert.equal(trigger.repeatRemaining, REPEAT, 'repeatRemaining');
				});
			});
		});

		it('should request ' + REPEAT + ' times and receive callback', () => {
			return requestAndWait(triggerId, null, null, REPEAT);
		});

		it('should be auto-deleted', () => {
			return TRIGGER.isAutoDeleted(triggerId);
		});
	}).timeout(300000);

	describe('Time-limited', () => {
		let triggerId;
		const START = 5,	// seconds later
			END = 15;		// seconds later

		it('should create trigger', () => {
			const start = moment().add(START, 'seconds').toDate(),
				end = moment().add(END, 'seconds').toDate();

			return TRIGGER.create({ start: start, end: end }).then(({ id }) => {
				triggerId = id.toString();
				return TRIGGER.handle(triggerId);
			});
		});

		it('should FAIL when requesting BEFORE start time', () => {
			return TRIGGER.request(triggerId).catch(res => {
				assert.equal(res.status, 404, 'status = 404');
			});
		});

		it('should request & get callback after start time', () => {
			return Promise.delay(START * 1000).then(() => {
				return requestAndWait(triggerId);
			});
		}).timeout((START + 1) * 1000);

		it('should FAIL when requesting AFTER end time', () => {
			return Promise.delay((END - START) * 1000).then(() => {
				return TRIGGER.request(triggerId).catch(res => {
					assert.equal(res.status, 404, 'status = 404');
				});
			});
		}).timeout((END - START + 2) * 1000);

		it('should be auto-deleted', () => {
			return TRIGGER.isAutoDeleted(triggerId);
		});
	});

	describe('Time-limited, Limited times', () => {
		let triggerId;

		it('should test combinations');
	});

	function requestAndWait(triggerId, param = {}, payload = {}, times = 1) {
		const requests = [],
			customData = Object.assign({}, payload, param);

		for (let i = 0; i < times; i++) {
			const extParam = Object.assign({}, param, { key: process.hrtime()[1] }); // used for matching callbacks)]);
			requests.push([triggerId, extParam, payload]);
		}
		return Promise.map(requests, (params) => _requestOnce(...params), { concurrency: CONCURRENCY });

		function _requestOnce(triggerId, param, payload) {
			return new Promise((resolve, reject) => {
				TRIGGER.waitForCallback(null, param).then(res => {
					delete res.key;
					delete res.echo;	// added by handle()
					assert.deepEqual(res, customData, 'payload');
					resolve(res);
				}).catch(err => reject(err));

				TRIGGER.request(triggerId, param);
			});
		}
	}

	before(() => {
		return app.init(null, true).then(() => {
			TRIGGER.startServer();
			return TRIGGER.deleteAll();
		});
	});

	after(() => TRIGGER.stopServer());
});
