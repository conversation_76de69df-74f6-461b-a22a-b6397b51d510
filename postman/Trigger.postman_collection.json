{"info": {"_postman_id": "c94174cc-f40c-43f8-a604-bcd37d59f1a1", "name": "<PERSON><PERSON>", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Create trigger", "event": [{"listen": "test", "script": {"id": "c7893aea-dd75-4a22-94d8-f3f23ebbb73f", "exec": ["var resp = pm.response.json();", " ", "pm.test(\"Create Trigger\", function () {", "    pm.response.to.have.status(200);", "    pm.environment.set(\"triggerId\", resp.id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "tenant-code", "value": "{{tenant}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Timer trigger #1\",\n    \"priority\": 0,\n    \"callback\": {\n        \"method\": \"post\",\n        \"url\": \"https://enww8o45nc8xodf.m.pipedream.net\",\n        \"payload\": {\n            \"name\": \"postman\"\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://{{host}}:8012/api/Triggers", "protocol": "http", "host": ["{{host}}"], "port": "8012", "path": ["api", "Triggers"]}}, "response": []}, {"name": "Create repeat timer trigger", "event": [{"listen": "test", "script": {"id": "461e897e-b11c-4fa0-8dc3-fc39fa170928", "exec": ["var resp = pm.response.json();", " ", "pm.test(\"Create Trigger\", function () {", "    pm.response.to.have.status(200);", "    pm.environment.set(\"triggerId\", resp.id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "tenant-code", "value": "{{tenant}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Repeat Timer trigger #5\",\n    \"priority\": 0,\n    \"repeatInterval\" : \"{{repeatInterval}}\",\n    \"repeatTimezone\" : \"Asia/Singapore\",\n    \"repeat\" : {{repeat}},\n    \"callback\": {\n        \"method\": \"post\",\n        \"url\": \"https://enww8o45nc8xodf.m.pipedream.net\",\n        \"payload\": {\n            \"name\": \"postman\"\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://{{host}}:8012/api/Triggers", "protocol": "http", "host": ["{{host}}"], "port": "8012", "path": ["api", "Triggers"]}}, "response": []}, {"name": "Schedule timer", "event": [{"listen": "test", "script": {"id": "618c6e58-88b4-4a42-8bda-6ca399c5ba6b", "exec": ["var resp = pm.response.json();", " ", "pm.test(\"Create Trigger\", function () {", "    pm.response.to.have.status(200);", "    pm.environment.set(\"triggerId\", resp.id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "tenant-code", "value": "{{tenant}}"}], "body": {"mode": "raw", "raw": "{\n    \"when\": \"30 seconds\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://{{host}}:8012/api/Triggers/{{triggerId}}/schedule", "protocol": "http", "host": ["{{host}}"], "port": "8012", "path": ["api", "Triggers", "{{triggerId}}", "schedule"]}}, "response": []}, {"name": "Stop trigger", "event": [{"listen": "test", "script": {"id": "572491b1-7d13-4c7f-b125-ffcfedeef565", "exec": ["var resp = pm.response.json();", " ", "pm.test(\"Create Trigger\", function () {", "    pm.response.to.have.status(200);", "    pm.environment.set(\"triggerId\", resp.id);", "});"], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "tenant-code", "value": "{{tenant}}"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://{{host}}:8012/api/Triggers/{{triggerId}}/stop", "protocol": "http", "host": ["{{host}}"], "port": "8012", "path": ["api", "Triggers", "{{triggerId}}", "stop"]}}, "response": []}, {"name": "Pause trigger", "event": [{"listen": "test", "script": {"id": "973b8790-beed-4151-9b0d-e72bceb52d66", "exec": ["var resp = pm.response.json();", " ", "pm.test(\"Create Trigger\", function () {", "    pm.response.to.have.status(200);", "    pm.environment.set(\"triggerId\", resp.id);", "});"], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "tenant-code", "value": "{{tenant}}"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://{{host}}:8012/api/Triggers/{{triggerId}}/pause", "protocol": "http", "host": ["{{host}}"], "port": "8012", "path": ["api", "Triggers", "{{triggerId}}", "pause"]}}, "response": []}, {"name": "Resume trigger", "event": [{"listen": "test", "script": {"id": "b0d5e4d5-0368-4593-bb2b-63ca557276a9", "exec": ["var resp = pm.response.json();", " ", "pm.test(\"Create Trigger\", function () {", "    pm.response.to.have.status(200);", "    pm.environment.set(\"triggerId\", resp.id);", "});"], "type": "text/javascript"}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "tenant-code", "value": "{{tenant}}"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://{{host}}:8012/api/Triggers/{{triggerId}}/resume", "protocol": "http", "host": ["{{host}}"], "port": "8012", "path": ["api", "Triggers", "{{triggerId}}", "resume"]}}, "response": []}, {"name": "Delete trigger", "event": [{"listen": "test", "script": {"id": "887d385e-2df4-407c-9147-0bb012b7ee60", "exec": ["var resp = pm.response.json();", " ", "pm.test(\"Create Trigger\", function () {", "    pm.response.to.have.status(200);", "    pm.environment.set(\"triggerId\", resp.id);", "});"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [{"key": "tenant-code", "value": "{{tenant}}"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://{{host}}:8012/api/Triggers/{{triggerId}}", "protocol": "http", "host": ["{{host}}"], "port": "8012", "path": ["api", "Triggers", "{{triggerId}}"]}}, "response": []}, {"name": "Request trigger (delay 5 secs)", "request": {"method": "POST", "header": [{"key": "tenant-code", "value": "dev_young"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "http://{{host}}:8012/api/Triggers/{{triggerId}}/request", "protocol": "http", "host": ["{{host}}"], "port": "8012", "path": ["api", "Triggers", "{{triggerId}}", "request"]}}, "response": []}], "protocolProfileBehavior": {}}